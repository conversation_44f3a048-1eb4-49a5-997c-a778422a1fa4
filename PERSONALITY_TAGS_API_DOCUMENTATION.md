# Personality Tags Generation API Documentation

## 📋 Overview

The new `generate-personality-tags` API endpoint provides a standalone service for generating personality tags based on Big Five personality trait scores. This API clones the exact logic from the `generate_synthetic_profile` function, allowing you to generate personality tags independently without creating a full synthetic profile.

## 🔧 API Endpoint

### Generate Personality Tags
**Endpoint:** `POST /generate-personality-tags`

**Description:** Generate personality tags based on Big Five personality trait scores

**Input Parameters:**
- `openness` (float): Openness to experience score (0-10)
- `conscientiousness` (float): Conscientiousness score (0-10)
- `extraversion` (float): Extraversion score (0-10)
- `agreeableness` (float): Agreeableness score (0-10)

**Note:** Neuroticism is automatically generated using the same random logic as the synthetic profile function.

## 📊 Personality Tag Mapping

The API uses the exact same mapping logic as the synthetic profile generation:

### Score Categories
- **Low (0-3):** Conventional, Spontaneous, Reserved, Independent, Calm
- **Medium (4-7):** Inquisitive, Adaptable, Friendly, Considerate, Perceptive
- **High (8-10):** Imaginative, Organised, Outgoing, Warm, Sensitive

### Trait Mapping
1. **Openness:** Imaginative (High) | Inquisitive (Medium) | Conventional (Low)
2. **Conscientiousness:** Organised (High) | Adaptable (Medium) | Spontaneous (Low)
3. **Extraversion:** Outgoing (High) | Friendly (Medium) | Reserved (Low)
4. **Agreeableness:** Warm (High) | Considerate (Medium) | Independent (Low)
5. **Neuroticism:** Sensitive (High) | Perceptive (Medium) | Calm (Low)

## 🔗 Example Usage

### Basic Request
```bash
curl -X POST "http://localhost:8200/generate-personality-tags" \
  -H "Content-Type: application/json" \
  -d '{
    "openness": 8.5,
    "conscientiousness": 6.0,
    "extraversion": 3.0,
    "agreeableness": 9.0
  }'
```

### Example Response
```json
{
    "status": "success",
    "data": {
        "personality_tags": "Imaginative, Adaptable, Reserved, Warm, Perceptive",
        "personality_tags_list": [
            "Imaginative",
            "Adaptable", 
            "Reserved",
            "Warm",
            "Perceptive"
        ],
        "trait_scores_used": {
            "openness": 8.5,
            "conscientiousness": 6.0,
            "extraversion": 3.0,
            "agreeableness": 9.0,
            "neuroticism": 7
        },
        "score_categories": {
            "openness": "High",
            "conscientiousness": "Medium",
            "extraversion": "Low",
            "agreeableness": "High",
            "neuroticism": "Medium"
        }
    },
    "generation_time_seconds": 0.000736
}
```

## 📈 Response Structure

### Success Response (200)
- `status`: "success"
- `data`: Object containing:
  - `personality_tags`: Comma-separated string of personality tags
  - `personality_tags_list`: Array of individual personality tags
  - `trait_scores_used`: Object with all trait scores (including generated neuroticism)
  - `score_categories`: Object showing High/Medium/Low category for each trait
- `generation_time_seconds`: Processing time in seconds

### Error Responses
- **400 Bad Request**: Invalid trait scores (outside 0-10 range)
- **422 Unprocessable Entity**: Missing required fields
- **500 Internal Server Error**: Server processing error

## 🧪 Testing Examples

### Test Case 1: High Scores
```json
{
    "openness": 9.0,
    "conscientiousness": 8.5,
    "extraversion": 9.5,
    "agreeableness": 8.0
}
```
**Expected Output:** "Imaginative, Organised, Outgoing, Warm, [Neuroticism-based]"

### Test Case 2: Medium Scores
```json
{
    "openness": 5.0,
    "conscientiousness": 6.0,
    "extraversion": 4.5,
    "agreeableness": 7.0
}
```
**Expected Output:** "Inquisitive, Adaptable, Friendly, Considerate, [Neuroticism-based]"

### Test Case 3: Low Scores
```json
{
    "openness": 2.0,
    "conscientiousness": 1.5,
    "extraversion": 3.0,
    "agreeableness": 2.5
}
```
**Expected Output:** "Conventional, Spontaneous, Reserved, Independent, [Neuroticism-based]"

### Test Case 4: Mixed Scores
```json
{
    "openness": 8.5,
    "conscientiousness": 5.0,
    "extraversion": 2.0,
    "agreeableness": 9.0
}
```
**Expected Output:** "Imaginative, Adaptable, Reserved, Warm, [Neuroticism-based]"

## 🔍 Input Validation

### Valid Ranges
- All trait scores must be between 0.0 and 10.0 (inclusive)
- Decimal values are accepted and recommended
- All four traits (openness, conscientiousness, extraversion, agreeableness) are required

### Invalid Input Examples
```json
// Negative score
{"openness": -1.0, "conscientiousness": 5.0, "extraversion": 5.0, "agreeableness": 5.0}

// Score too high
{"openness": 5.0, "conscientiousness": 11.0, "extraversion": 5.0, "agreeableness": 5.0}

// Missing field
{"openness": 5.0, "conscientiousness": 5.0, "extraversion": 5.0}
```

## ⚡ Performance

### Response Times
- **Typical:** 0.001-0.003 seconds
- **Maximum observed:** 0.264 seconds (first request with cold start)
- **Average:** ~0.01 seconds

### Scalability
- Lightweight computation (no external API calls)
- Stateless operation (no database dependencies)
- Suitable for high-frequency requests

## 🔄 Consistency with Synthetic Profile Generation

### Verification
The API has been tested to ensure **100% consistency** with the personality tags generation logic in `generate_synthetic_profile`:

✅ **Same mapping logic**
✅ **Same score categorization (≤3, ≤7, >7)**
✅ **Same neuroticism generation (random Gaussian)**
✅ **Same tag selection per trait position**

### Test Results
- **Consistency Test:** PASSED
- **Both methods produce identical first 4 tags**
- **Neuroticism varies as expected (random generation)**

## 🎯 Use Cases

### 1. Standalone Personality Assessment
Generate personality tags for user profiles without creating synthetic data.

### 2. A/B Testing
Test different personality tag combinations for recommendation algorithms.

### 3. Profile Completion
Fill in missing personality tags for incomplete user profiles.

### 4. Research & Analytics
Analyze personality tag distributions across different trait score ranges.

### 5. Integration Testing
Test personality-based matching algorithms with controlled inputs.

## 🔧 Integration Examples

### Python Integration
```python
import requests

def generate_personality_tags(openness, conscientiousness, extraversion, agreeableness):
    url = "http://localhost:8200/generate-personality-tags"
    data = {
        "openness": openness,
        "conscientiousness": conscientiousness,
        "extraversion": extraversion,
        "agreeableness": agreeableness
    }
    
    response = requests.post(url, json=data)
    if response.status_code == 200:
        result = response.json()
        return result['data']['personality_tags']
    else:
        raise Exception(f"API error: {response.status_code}")

# Example usage
tags = generate_personality_tags(8.5, 6.0, 3.0, 9.0)
print(f"Generated tags: {tags}")
```

### JavaScript Integration
```javascript
async function generatePersonalityTags(openness, conscientiousness, extraversion, agreeableness) {
    const url = 'http://localhost:8200/generate-personality-tags';
    const data = {
        openness,
        conscientiousness,
        extraversion,
        agreeableness
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            return result.data.personality_tags;
        } else {
            throw new Error(`API error: ${response.status}`);
        }
    } catch (error) {
        console.error('Error generating personality tags:', error);
        throw error;
    }
}

// Example usage
generatePersonalityTags(8.5, 6.0, 3.0, 9.0)
    .then(tags => console.log(`Generated tags: ${tags}`))
    .catch(error => console.error('Error:', error));
```

## 📋 Summary

The new personality tags generation API provides:

- ✅ **Exact logic clone** from synthetic profile generation
- ✅ **Standalone functionality** independent of profile creation
- ✅ **Fast performance** (~0.01 seconds average)
- ✅ **Comprehensive validation** with clear error messages
- ✅ **Detailed response** with tags, categories, and metadata
- ✅ **100% consistency** verified through automated testing
- ✅ **Easy integration** with clear documentation and examples

This API enables flexible personality tag generation for various use cases while maintaining complete consistency with the existing synthetic profile system! 🎯
