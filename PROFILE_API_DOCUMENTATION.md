# Profile Data Retrieval API Documentation

## 📋 Overview

New API endpoints have been added to retrieve profile data from the `dating_app_sample_100_essay_tag.csv` file. These endpoints provide easy access to the dating profile database used for matching algorithms.

## 🔧 API Endpoints

### 1. Get Profile by ID
**Endpoint:** `GET /profile/{id}`

**Description:** Retrieve a single profile by its unique ID

**Parameters:**
- `id` (path parameter): Integer ID of the profile to retrieve

**Example Request:**
```bash
curl -X GET "http://localhost:8200/profile/17"
```

**Example Response:**
```json
{
    "status": "success",
    "data": {
        "id": 17,
        "file_name": "M_48_image_17.jpg",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>",
        "height_cm": 177.8,
        "age": 48,
        "status": "single",
        "gender": "m",
        "orientation": "straight",
        "body_type": "a little extra",
        "diet": "mostly anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "High school diploma",
        "job": "computer / hardware / software",
        "location": "walnut creek, california",
        "family_plan": "has kids, and might want more",
        "pets": "likes dogs and likes cats",
        "desired_relationship": "Long-term relationship",
        "openness": 8.0,
        "conscientiousness": 6.0,
        "extraversion": 7.0,
        "agreeableness": 8.0,
        "neuroticism": 3.0,
        "interests": "Pickleball, Stand-Up Comedy, Taekwondo, Vinyl Records Collecting, Judo",
        "essay0": "i'm an open book for the most part...",
        "personality_tags": "Imaginative, Adaptable, Friendly, Warm, Calm"
    }
}
```

**Error Responses:**
- `404 Not Found`: Profile with specified ID does not exist
- `400 Bad Request`: Invalid ID format
- `500 Internal Server Error`: Server error

### 2. Get All Profiles with Pagination
**Endpoint:** `GET /profiles`

**Description:** Retrieve multiple profiles with optional pagination

**Query Parameters:**
- `skip` (optional): Number of profiles to skip (default: 0)
- `limit` (optional): Maximum number of profiles to return (default: 100)

**Example Requests:**
```bash
# Get first 5 profiles
curl -X GET "http://localhost:8200/profiles?skip=0&limit=5"

# Get next 10 profiles
curl -X GET "http://localhost:8200/profiles?skip=5&limit=10"

# Get all profiles (default)
curl -X GET "http://localhost:8200/profiles"
```

**Example Response:**
```json
{
    "status": "success",
    "data": {
        "profiles": [
            {
                "id": 17,
                "first_name": "James",
                "last_name": "Hall",
                "gender": "m",
                "age": 48,
                // ... full profile data
            },
            {
                "id": 112,
                "first_name": "Justin",
                "last_name": "Thomas",
                "gender": "m",
                "age": 36,
                // ... full profile data
            }
        ],
        "count": 2,
        "skip": 0,
        "limit": 5
    }
}
```

## 📊 Data Structure

Each profile contains the following fields:

### Basic Information
- `id`: Unique profile identifier (integer)
- `file_name`: Associated image filename (string)
- `first_name`: First name (string)
- `last_name`: Last name (string)
- `height_cm`: Height in centimeters (float)
- `age`: Age in years (integer)
- `status`: Relationship status (string)
- `gender`: Gender (string: 'm' or 'f')
- `orientation`: Sexual orientation (string)

### Lifestyle & Preferences
- `body_type`: Body type description (string)
- `diet`: Dietary preferences (string)
- `drinks`: Drinking habits (string)
- `smokes`: Smoking habits (string)
- `education`: Education level (string)
- `job`: Job/profession (string)
- `location`: Geographic location (string)
- `family_plan`: Family planning preferences (string)
- `pets`: Pet preferences (string)
- `desired_relationship`: Relationship goals (string)

### Personality Metrics
- `openness`: Openness score (float, 0-10)
- `conscientiousness`: Conscientiousness score (float, 0-10)
- `extraversion`: Extraversion score (float, 0-10)
- `agreeableness`: Agreeableness score (float, 0-10)
- `neuroticism`: Neuroticism score (float, 0-10)

### Personal Content
- `interests`: Comma-separated list of interests (string)
- `essay0`: Personal essay/description (string)
- `personality_tags`: Comma-separated personality tags (string)

## 🧪 Testing

### Test Script
Run the comprehensive test suite:
```bash
python tests/test_profile_api.py
```

### Test Coverage
The test script validates:
- ✅ Single profile retrieval by ID
- ✅ Profile not found handling (404 errors)
- ✅ Pagination functionality
- ✅ Data type validation
- ✅ Required field presence
- ✅ Response time performance

## 🔗 Integration Examples

### Python Integration
```python
import requests

# Get a specific profile
response = requests.get("http://localhost:8200/profile/17")
if response.status_code == 200:
    profile = response.json()['data']
    print(f"Profile: {profile['first_name']} {profile['last_name']}")

# Get multiple profiles
response = requests.get("http://localhost:8200/profiles?limit=10")
if response.status_code == 200:
    profiles = response.json()['data']['profiles']
    for profile in profiles:
        print(f"ID {profile['id']}: {profile['first_name']}")
```

### JavaScript Integration
```javascript
// Get a specific profile
fetch('http://localhost:8200/profile/17')
  .then(response => response.json())
  .then(data => {
    const profile = data.data;
    console.log(`Profile: ${profile.first_name} ${profile.last_name}`);
  });

// Get multiple profiles
fetch('http://localhost:8200/profiles?limit=10')
  .then(response => response.json())
  .then(data => {
    const profiles = data.data.profiles;
    profiles.forEach(profile => {
      console.log(`ID ${profile.id}: ${profile.first_name}`);
    });
  });
```

## 📈 Performance

### Response Times
- **Single profile:** ~0.005-0.137 seconds
- **Multiple profiles (5):** ~0.021 seconds
- **All profiles (100):** ~0.041 seconds

### Database Information
- **Total profiles:** 100
- **File source:** `files/dating_app_sample_100_essay_tag.csv`
- **Data format:** CSV with headers
- **Encoding:** UTF-8

## 🚀 Use Cases

### 1. Profile Browsing
Build user interfaces that allow browsing through dating profiles with pagination.

### 2. Profile Details
Display detailed profile information when users click on specific profiles.

### 3. Data Analysis
Extract profile data for analytics, matching algorithm development, or research.

### 4. Testing & Development
Use real profile data for testing synthetic profile generation and matching algorithms.

### 5. API Integration
Integrate with existing dating app backends or recommendation systems.

## 🔧 Error Handling

### Common Error Scenarios
1. **Profile Not Found (404):** Requested profile ID doesn't exist
2. **Invalid ID (400):** Non-numeric or malformed profile ID
3. **Server Error (500):** CSV file missing or corrupted

### Best Practices
- Always check response status codes
- Handle 404 errors gracefully for user-facing applications
- Implement retry logic for 500 errors
- Use appropriate timeout values for requests

## 📋 Summary

The new profile retrieval APIs provide:
- ✅ **Fast access** to dating profile database
- ✅ **Flexible pagination** for large datasets
- ✅ **Complete data structure** with all profile fields
- ✅ **Proper error handling** and validation
- ✅ **Easy integration** with existing applications
- ✅ **Comprehensive testing** and documentation

These endpoints complement the existing synthetic profile generation and matching APIs, providing a complete solution for dating app development and research! 🎯
