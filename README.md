# Image Upload API for Google Cloud Storage

A FastAPI-based web service for uploading images to Google Cloud Storage bucket `comfyui-data-analytic-project-424703` in the `comfyui/input` folder.

## Features

- ✅ Upload images to Google Cloud Storage
- ✅ Automatic image validation (format, size, content)
- ✅ Unique filename generation with timestamps
- ✅ Public URL generation for uploaded images
- ✅ Support for multiple image formats (JPEG, PNG, GIF, WebP, BMP, TIFF)
- ✅ File size validation (max 10MB)
- ✅ CORS enabled for web frontend integration
- ✅ Comprehensive error handling and logging
- ✅ Health check endpoints
- ✅ Interactive API documentation
- ✅ Test HTML page included

## Setup

### Option 1: Docker (Recommended)

1. **Ensure Docker is installed and running**

2. **Make sure your GCP credentials are in place**:
   ```bash
   # Your gcp-credentials.json should be in the project root
   ls gcp-credentials.json
   ```

3. **Start the application**:
   ```bash
   # Build and start the container
   docker-compose up -d --build

   # View logs
   docker-compose logs -f

   # Stop the container
   docker-compose down
   ```

### Option 2: Local Python Setup

#### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

#### 2. Google Cloud Setup

Make sure your `gcp-credentials.json` file is in the project root with the correct service account credentials that have access to the bucket `comfyui-data-analytic-project-424703`.

#### 3. Run the Server

```bash
python run_server.py
```

Or directly with uvicorn:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## API Endpoints

### Upload Image (File)
- **POST** `/upload-image`
- Upload an image file to Google Cloud Storage
- **Body**: Form data with `file` field containing the image
- **Response**: JSON with upload status and public URL

### Upload Image (Base64)
- **POST** `/upload-image-base64`
- Upload an image from base64 encoded data
- **Body**: JSON with `file` field containing base64 data
- **Response**: JSON with upload status and public URL

### Execute ComfyUI Workflow
- **POST** `/execute-workflow`
- Execute ComfyUI workflow with prompt and image URL
- **Body**: JSON with `prompt`, `image_url`, `weight` (optional), and other optional parameters
- **Response**: JSON with workflow results and execution timing
- **Weight**: Controls PuLID strength and safety threshold (0.0-1.0, default: 0.7)

### Create Synthetic Profile
- **POST** `/create-synthetic-profile`
- Generate a synthetic dating profile based on input profile data
- **Body**: JSON with profile information (height, age, gender, personality scores, interests, etc.)
- **Response**: JSON with generated synthetic profile and modification details
- **Logic**: Flips gender, adjusts height, modifies interests, and randomizes personality traits

### Health Check
- **GET** `/health`
- Check API and GCP connectivity status

### Root
- **GET** `/`
- API information and available endpoints

### Delete Image
- **DELETE** `/delete-image/{filename}`
- Delete an image from Google Cloud Storage

## Usage Examples

### Using curl

```bash
# Upload an image (file)
curl -X POST "http://localhost:8200/upload-image" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@your_image.jpg"

# Upload an image (base64)
curl -X POST "http://localhost:8200/upload-image-base64" \
     -H "Content-Type: application/json" \
     -d '{
       "file": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
     }'

# Execute ComfyUI workflow
curl -X POST "http://localhost:8200/execute-workflow" \
     -H "Content-Type: application/json" \
     -d '{
       "prompt": "portrait photo of young woman, smiling, casual style",
       "image_url": "https://storage.googleapis.com/your-bucket/image.jpg",
       "weight": 0.7
     }'

# Health check
curl -X GET "http://localhost:8200/health"
```

### Using the Test Page

1. Start the server: `python run_server.py`
2. Open `test_upload.html` in your web browser
3. Select or drag & drop an image file
4. Click "Upload Image"
5. View the result with the public URL

### Using Python requests

```python
import requests
import base64

# Upload image (file)
with open('your_image.jpg', 'rb') as f:
    files = {'file': f}
    response = requests.post('http://localhost:8200/upload-image', files=files)
    result = response.json()
    print(f"Public URL: {result['data']['public_url']}")

# Upload image (base64)
with open('your_image.jpg', 'rb') as f:
    image_data = base64.b64encode(f.read()).decode()
    data = {"file": f"data:image/jpeg;base64,{image_data}"}
    response = requests.post('http://localhost:8200/upload-image-base64', json=data)
    result = response.json()
    print(f"Public URL: {result['data']['public_url']}")

# Execute ComfyUI workflow
workflow_data = {
    "prompt": "portrait photo of young woman, smiling, casual style",
    "image_url": "https://storage.googleapis.com/your-bucket/image.jpg",
    "weight": 0.7,
    "cloudrun_url": "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app",
    "output_node": "51"
}
response = requests.post('http://localhost:8200/execute-workflow', json=workflow_data)

# Create synthetic profile
profile_data = {
    "height_cm": 177.8,
    "age": 28,
    "gender": "m",
    "orientation": "straight",
    "body_type": "average",
    "diet": "mostly anything",
    "drinks": "socially",
    "smokes": "no",
    "education": "Bachelor's degree",
    "job": "computer / hardware / software",
    "location": "san francisco, california",
    "family_plan": "want kids",
    "pets": "likes dogs",
    "desired_relationship": "Long-term relationship",
    "openness": 8.0,
    "conscientiousness": 6.0,
    "extraversion": 7.0,
    "agreeableness": 8.0,
    "neuroticism": 3.0,
    "interests": ["Pickleball", "Stand-Up Comedy", "Taekwondo", "Vinyl Records Collecting", "Judo"],
    "essay0": "I'm an open book for the most part..."
}
response = requests.post('http://localhost:8200/create-synthetic-profile', json=profile_data)
result = response.json()
synthetic_profile = result['data']['synthetic_profile']
print(f"Generated synthetic profile with gender: {synthetic_profile['gender']}")
response = requests.post('http://localhost:8200/execute-workflow', json=workflow_data)
result = response.json()
print(f"Output URL: {result['data']['output_url']}")
print(f"Execution time: {result['data']['execution_time_seconds']} seconds")
```

## Configuration

The following settings can be modified in `main.py`:

- `BUCKET_NAME`: Google Cloud Storage bucket name
- `FOLDER_PATH`: Folder path within the bucket
- `CREDENTIALS_PATH`: Path to GCP service account credentials
- `MAX_FILE_SIZE`: Maximum allowed file size (default: 10MB)
- `SUPPORTED_IMAGE_TYPES`: Supported image MIME types

## Docker Configuration

### Features

- **Health checks** for container monitoring
- **Volume mounts** for persistent logs and credentials
- **Automatic restart** on failure
- **Simple single-service setup**

## File Structure

```
.
├── main.py                 # FastAPI application
├── gcp_storage.py         # Google Cloud Storage client
├── gcp-credentials.json   # GCP service account credentials
├── requirements.txt       # Python dependencies
├── run_server.py         # Server startup script
├── test_upload.html      # Test web page
├── Dockerfile            # Docker image definition
├── docker-compose.yml    # Docker setup
├── .dockerignore         # Docker ignore file
└── README.md             # This file
```

## API Documentation

Once the server is running, visit:
- **Interactive docs**: http://localhost:8200/docs
- **ReDoc**: http://localhost:8200/redoc

## Error Handling

The API includes comprehensive error handling for:
- Invalid file formats
- File size limits
- GCP connectivity issues
- Authentication problems
- Network errors

## Security Notes

- The current CORS configuration allows all origins (`*`) for development
- In production, update the CORS settings to only allow specific domains
- Ensure your GCP service account has minimal required permissions
- Consider implementing rate limiting for production use

## Troubleshooting

### Common Issues

1. **"GCP Storage service is not available"**
   - Check that `gcp-credentials.json` exists and is valid
   - Verify the service account has access to the bucket

2. **"Bucket does not exist"**
   - Ensure the bucket name is correct
   - Check that the service account has access to the bucket

3. **"Could not set individual ACL on blob"**
   - This is normal if uniform bucket-level access is enabled
   - The file will still be publicly accessible if the bucket is configured for public access

4. **File upload fails**
   - Check file size (must be ≤ 10MB)
   - Verify file format is supported
   - Check network connectivity to Google Cloud

## License

This project is provided as-is for educational and development purposes.
