# Synthetic Profile Matching API Documentation

## Overview

The Synthetic Profile Matching API creates synthetic dating profiles and finds matches using the complete logic from `synthetic_profile_matching.py`. It includes face embedding matching, personality matching, interest matching, and AI-generated explanations using Gemini AI.

## Endpoint

**POST** `/create-synthetic-profile-matching`

Creates a synthetic profile, performs comprehensive matching against a dating database, and generates AI explanations for top matches.

## Request Format

### Headers
```
Content-Type: application/json
```

### Request Body

```json
{
  "file_name": "M_48_image_17.jpg",
  "first_name": "<PERSON>",
  "last_name": "<PERSON>",
  "height_cm": 177.8,
  "age": 48,
  "status": "single",
  "gender": "m",
  "orientation": "straight",
  "body_type": "a little extra",
  "diet": "mostly anything",
  "drinks": "socially",
  "smokes": "no",
  "education": "High school diploma",
  "job": "computer / hardware / software",
  "location": "walnut creek, california",
  "family_plan": "has kids, and might want more",
  "pets": "likes dogs and likes cats",
  "desired_relationship": "Long-term relationship",
  "openness": 8.0,
  "conscientiousness": 6.0,
  "extraversion": 7.0,
  "agreeableness": 8.0,
  "neuroticism": 3.0,
  "interests": "Pickleball, Stand-Up Comedy, Taekwondo, Vinyl Records Collecting, Judo",
  "essay0": "i'm an open book for the most part. i believe in openness and honesty...",
  "personality_tags": "Imaginative, Adaptable, Friendly, Warm, Calm"
}
```

### Required Fields

- `file_name` (string): Image file path for face embedding
- `first_name` (string): First name
- `last_name` (string): Last name
- `height_cm` (float): Height in centimeters
- `age` (int): Age in years
- `status` (string): Relationship status
- `gender` (string): Gender ('m' or 'f')
- `orientation` (string): Sexual orientation
- `body_type` (string): Body type description
- `diet` (string): Dietary preferences
- `drinks` (string): Drinking habits
- `smokes` (string): Smoking habits
- `education` (string): Education level
- `job` (string): Job/profession
- `location` (string): Location
- `family_plan` (string): Family planning preferences
- `pets` (string): Pet preferences
- `desired_relationship` (string): Relationship goals
- `openness` (float): Personality score (0-10)
- `conscientiousness` (float): Personality score (0-10)
- `extraversion` (float): Personality score (0-10)
- `agreeableness` (float): Personality score (0-10)
- `neuroticism` (float): Personality score (0-10)
- `interests` (string): Comma-separated interests string
- `essay0` (string): Profile essay text
- `personality_tags` (string): Comma-separated personality tags

## Response Format

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Synthetic profile created and matches found successfully",
  "data": {
    "synthetic_profile": {
      "height_cm": 172.8,
      "age": 48,
      "status": "single",
      "gender": "f",
      "orientation": "straight",
      "body_type": "a little extra",
      "diet": "mostly anything",
      "drinks": "socially",
      "smokes": "no",
      "education": "High school diploma",
      "job": "computer / hardware / software",
      "location": "walnut creek, california",
      "family_plan": "has kids, and might want more",
      "pets": "likes dogs and likes cats",
      "desired_relationship": "Long-term relationship",
      "openness": 9,
      "conscientiousness": 7,
      "extraversion": 8,
      "agreeableness": 6,
      "neuroticism": 4,
      "interests": ["Stand-Up Comedy", "Vinyl Records Collecting", "Judo", "Painting", "Yoga"],
      "personality_tags": ["Imaginative", "Adaptable", "Outgoing", "Considerate", "Perceptive"]
    },
    "matches": {
      "match_df": [
        {
          "id": 112,
          "gender": "f",
          "same_interests": 2,
          "same_personality": 3,
          "cosine_score": 0.85,
          "same_interests_norm": 0.4,
          "same_personality_norm": 0.6,
          "cosine_score_norm": 0.925,
          "weighted_average": 0.795,
          "file_name": "F_36_image_112.png"
        }
      ],
      "top_5_with_explanations": [
        {
          "id": 112,
          "weighted_average": 0.795,
          "file_name": "F_36_image_112.png",
          "first_name": "Sarah",
          "interests": ["Vinyl Records Collecting", "Ballet", "Pilates"],
          "personality_tags": ["Imaginative", "Adaptable", "Friendly"],
          "explanation": "Both you and Sarah share a love for vinyl records collecting and have imaginative, adaptable personalities that complement each other beautifully."
        }
      ],
      "total_candidates": 45
    },
    "generation_time_seconds": 2.145
  }
}
```

## Synthetic Profile Generation and Matching Logic

The API implements the complete logic from `synthetic_profile_matching.py`:

### 1. Synthetic Profile Generation

#### Gender and Height
- **Gender**: Flipped (m → f, f → m)
- **Height**: Adjusted by ±5cm (male input -5cm, female input +5cm)

#### Interests
- **Keep**: 3 randomly selected interests from the original comma-separated list
- **Add**: 2 new random interests from the hobbies database (`files/hobby.json`)
- **Total**: Up to 5 interests in the synthetic profile

#### Personality Scores
- **Neuroticism**: Generated randomly (normal distribution, mean=4.5, std=1)
- **Other traits**: Original scores + random noise (normal distribution, mean=0, std=2)
- **Range**: All scores clipped to 0-10 and rounded to integers

#### Personality Tags
Generated based on personality scores using predefined mappings:
- **Low (0-3)**: Conventional, Spontaneous, Reserved, Independent, Calm
- **Medium (4-7)**: Inquisitive, Adaptable, Friendly, Considerate, Perceptive
- **High (8-10)**: Imaginative, Organised, Outgoing, Warm, Sensitive

### 2. Matching Process

#### Face Embedding Matching
- Extracts face embedding from input image (currently simulated)
- Compares with pre-computed embeddings from `files/dating_add_sample_100_embedding.pickle`
- Uses cosine similarity for face comparison
- Filters candidates by opposite gender

#### Personality Matching
- Compares synthetic personality tags with candidate personality tags
- Counts overlapping personality traits
- Scores based on number of shared traits (0-5)

#### Interest Matching
- Compares synthetic interests with candidate interests
- Counts overlapping interests
- Scores based on number of shared interests (0-5)

#### Combined Scoring
- **Normalization**: All scores normalized to 0-1 scale
- **Weighted Average**:
  - Interests: 20% weight
  - Personality: 20% weight
  - Face similarity: 60% weight
- **Ranking**: Candidates sorted by weighted average score

### 3. AI Explanations
- Uses Gemini AI (gemini-2.5-flash model) for top 5 matches
- Generates 30-50 word explanations based on shared interests and personality
- Personalizes explanations with candidate names
- Includes rate limiting (1 second between requests)

## Error Responses

### 400 Bad Request
```json
{
  "detail": "At least one interest must be provided"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error: [error message]"
}
```

## Testing

Use the provided test script:

```bash
python test_synthetic_profile_api.py
```

## Dependencies

### Required Files
- `files/hobby.json`: Database of available hobbies/interests
- `files/dating_add_sample_100_essay_tag.csv`: Dating profiles dataset
- `files/dating_add_sample_100_embedding.pickle`: Pre-computed face embeddings

### Python Packages
- `fastapi`: Web framework
- `pandas`: Data manipulation
- `numpy`: Numerical computations
- `opencv-python`: Image processing (for face embedding)
- `google-generativeai`: Gemini AI integration
- `pickle`: Data serialization

### API Keys
- Gemini AI API key: `AIzaSyBDHaLI-2FQGzH5dbJIHEYgDCcFjAlDN08` (hardcoded in implementation)

## Example Usage

### Python
```python
import requests

profile_data = {
    "file_name": "M_48_image_17.jpg",
    "first_name": "James",
    "last_name": "Hall",
    "height_cm": 177.8,
    "age": 48,
    "status": "single",
    "gender": "m",
    "orientation": "straight",
    "body_type": "a little extra",
    "diet": "mostly anything",
    "drinks": "socially",
    "smokes": "no",
    "education": "High school diploma",
    "job": "computer / hardware / software",
    "location": "walnut creek, california",
    "family_plan": "has kids, and might want more",
    "pets": "likes dogs and likes cats",
    "desired_relationship": "Long-term relationship",
    "openness": 8.0,
    "conscientiousness": 6.0,
    "extraversion": 7.0,
    "agreeableness": 8.0,
    "neuroticism": 3.0,
    "interests": "Pickleball, Stand-Up Comedy, Taekwondo, Vinyl Records Collecting, Judo",
    "essay0": "i'm an open book for the most part...",
    "personality_tags": "Imaginative, Adaptable, Friendly, Warm, Calm"
}

response = requests.post(
    "http://localhost:8000/create-synthetic-profile-matching",
    json=profile_data,
    timeout=60
)

if response.status_code == 200:
    result = response.json()
    synthetic_profile = result["data"]["synthetic_profile"]
    matches = result["data"]["matches"]

    print(f"Generated synthetic profile: {synthetic_profile['gender']}")
    print(f"Found {matches['total_candidates']} matches")
    print(f"Top match: {matches['top_5_with_explanations'][0]['first_name']}")
```

### Testing
```bash
# Run the test script
python test_synthetic_profile_matching_api.py
```
