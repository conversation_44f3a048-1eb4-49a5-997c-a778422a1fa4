# Synthetic Profile API Documentation

## Overview

The Synthetic Profile API creates synthetic dating profiles based on input profile data. It implements the same logic as the original `synthetic_profile_matching.py` script but accepts data via API requests instead of reading from CSV files.

## Endpoint

**POST** `/create-synthetic-profile`

Creates a synthetic profile by modifying the input profile data according to predefined rules.

## Request Format

### Headers
```
Content-Type: application/json
```

### Request Body

```json
{
  "height_cm": 177.8,
  "age": 28,
  "gender": "m",
  "orientation": "straight",
  "body_type": "average",
  "diet": "mostly anything",
  "drinks": "socially",
  "smokes": "no",
  "education": "Bachelor's degree",
  "job": "computer / hardware / software",
  "location": "san francisco, california",
  "family_plan": "want kids",
  "pets": "likes dogs",
  "desired_relationship": "Long-term relationship",
  "openness": 8.0,
  "conscientiousness": 6.0,
  "extraversion": 7.0,
  "agreeableness": 8.0,
  "neuroticism": 3.0,
  "interests": [
    "Pickleball",
    "Stand-Up Comedy",
    "Taekwondo",
    "Vinyl Records Collecting",
    "Judo"
  ],
  "essay0": "I'm an open book for the most part..."
}
```

### Required Fields

- `height_cm` (float): Height in centimeters
- `age` (int): Age in years
- `gender` (string): Gender ('m' or 'f')
- `orientation` (string): Sexual orientation
- `body_type` (string): Body type description
- `diet` (string): Dietary preferences
- `drinks` (string): Drinking habits
- `smokes` (string): Smoking habits
- `education` (string): Education level
- `job` (string): Job/profession
- `location` (string): Location
- `family_plan` (string): Family planning preferences
- `pets` (string): Pet preferences
- `desired_relationship` (string): Relationship goals
- `openness` (float): Personality score (0-10)
- `conscientiousness` (float): Personality score (0-10)
- `extraversion` (float): Personality score (0-10)
- `agreeableness` (float): Personality score (0-10)
- `interests` (array): List of interest strings

### Optional Fields

- `neuroticism` (float): Personality score (0-10). If not provided, will be randomly generated
- `essay0` (string): Profile essay text

## Response Format

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Synthetic profile created successfully",
  "data": {
    "synthetic_profile": {
      "height_cm": 172.8,
      "age": 28,
      "gender": "f",
      "orientation": "straight",
      "body_type": "average",
      "diet": "mostly anything",
      "drinks": "socially",
      "smokes": "no",
      "education": "Bachelor's degree",
      "job": "computer / hardware / software",
      "location": "san francisco, california",
      "family_plan": "want kids",
      "pets": "likes dogs",
      "desired_relationship": "Long-term relationship",
      "openness": 9,
      "conscientiousness": 7,
      "extraversion": 8,
      "agreeableness": 6,
      "neuroticism": 4,
      "interests": [
        "Stand-Up Comedy",
        "Vinyl Records Collecting",
        "Judo",
        "Painting",
        "Yoga"
      ],
      "essay0": "I'm an open book for the most part...",
      "personality_tags": [
        "Imaginative",
        "Adaptable", 
        "Outgoing",
        "Considerate",
        "Perceptive"
      ],
      "generated_at": 1704067200.123
    },
    "generation_time_seconds": 0.045,
    "original_input": {
      "gender": "m",
      "height_cm": 177.8,
      "age": 28,
      "interests_count": 5,
      "original_interests": [
        "Pickleball",
        "Stand-Up Comedy",
        "Taekwondo", 
        "Vinyl Records Collecting",
        "Judo"
      ]
    },
    "modifications": {
      "gender_flipped": "m -> f",
      "height_adjusted": "177.8 -> 172.8",
      "interests_modified": "5 -> 5",
      "personality_randomized": true
    }
  }
}
```

## Synthetic Profile Generation Logic

The API applies the following transformations to create a synthetic profile:

### 1. Gender and Height
- **Gender**: Flipped (m → f, f → m)
- **Height**: Adjusted by ±5cm (male input -5cm, female input +5cm)

### 2. Interests
- **Keep**: 3 randomly selected interests from the original list
- **Add**: 2 new random interests from the hobbies database
- **Total**: Up to 5 interests in the synthetic profile

### 3. Personality Scores
- **Neuroticism**: Generated randomly (normal distribution, mean=4.5, std=1) if not provided
- **Other traits**: Original scores + random noise (normal distribution, mean=0, std=2)
- **Range**: All scores clipped to 0-10 and rounded to integers

### 4. Personality Tags
Generated based on personality scores using predefined mappings:
- **Low (0-3)**: Conventional, Spontaneous, Reserved, Independent, Calm
- **Medium (4-7)**: Inquisitive, Adaptable, Friendly, Considerate, Perceptive  
- **High (8-10)**: Imaginative, Organised, Outgoing, Warm, Sensitive

## Error Responses

### 400 Bad Request
```json
{
  "detail": "At least one interest must be provided"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error: [error message]"
}
```

## Testing

Use the provided test script:

```bash
python test_synthetic_profile_api.py
```

## Dependencies

The API requires the following files:
- `files/hobby.json`: Database of available hobbies/interests

## Example Usage

### Python
```python
import requests

profile_data = {
    "height_cm": 175.0,
    "age": 25,
    "gender": "f",
    # ... other required fields
    "interests": ["Reading", "Hiking", "Photography"]
}

response = requests.post(
    "http://localhost:8000/create-synthetic-profile",
    json=profile_data
)

if response.status_code == 200:
    result = response.json()
    synthetic_profile = result["data"]["synthetic_profile"]
    print(f"Generated synthetic profile with gender: {synthetic_profile['gender']}")
```

### cURL
```bash
curl -X POST "http://localhost:8000/create-synthetic-profile" \
  -H "Content-Type: application/json" \
  -d '{
    "height_cm": 175.0,
    "age": 25,
    "gender": "f",
    "interests": ["Reading", "Hiking", "Photography"],
    "openness": 7.0,
    "conscientiousness": 6.0,
    "extraversion": 8.0,
    "agreeableness": 9.0
  }'
```
