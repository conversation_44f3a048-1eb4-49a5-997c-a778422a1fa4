#!/usr/bin/env python3
"""
Example usage of the Synthetic Profile API.

This script demonstrates how to use the new /create-synthetic-profile endpoint
to generate synthetic dating profiles from input data.
"""

import requests
import json
import time

def create_synthetic_profile_example():
    """Example of creating a synthetic profile via API."""
    
    # API endpoint
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/create-synthetic-profile"
    
    # Example profile data (based on the original CSV structure)
    profile_data = {
        "height_cm": 177.8,
        "age": 28,
        "gender": "m",  # Will be flipped to 'f' in synthetic profile
        "orientation": "straight",
        "body_type": "average",
        "diet": "mostly anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "Bachelor's degree",
        "job": "computer / hardware / software",
        "location": "san francisco, california",
        "family_plan": "want kids",
        "pets": "likes dogs",
        "desired_relationship": "Long-term relationship",
        
        # Personality scores (0-10)
        "openness": 8.0,
        "conscientiousness": 6.0,
        "extraversion": 7.0,
        "agreeableness": 8.0,
        "neuroticism": 3.0,  # Optional - will be randomly generated if not provided
        
        # Interests (will be modified in synthetic profile)
        "interests": [
            "Pickleball",
            "Stand-Up Comedy", 
            "Taekwondo",
            "Vinyl Records Collecting",
            "Judo"
        ],
        
        # Optional essay
        "essay0": "I'm an open book for the most part. I believe in openness and honesty."
    }
    
    print("🤖 Synthetic Profile API Example")
    print("=" * 50)
    
    print("📝 Original Profile Data:")
    print(f"   Gender: {profile_data['gender']}")
    print(f"   Height: {profile_data['height_cm']} cm")
    print(f"   Age: {profile_data['age']}")
    print(f"   Interests: {profile_data['interests']}")
    print(f"   Personality: O={profile_data['openness']}, C={profile_data['conscientiousness']}, E={profile_data['extraversion']}, A={profile_data['agreeableness']}, N={profile_data['neuroticism']}")
    
    try:
        print(f"\n📡 Sending request to: {endpoint}")
        
        # Make the API request
        response = requests.post(endpoint, json=profile_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Success!")
            print(f"⏱️  Generation time: {result['data']['generation_time_seconds']} seconds")
            
            # Extract synthetic profile
            synthetic = result['data']['synthetic_profile']
            
            print(f"\n🎭 Generated Synthetic Profile:")
            print(f"   Gender: {synthetic['gender']} (flipped from {profile_data['gender']})")
            print(f"   Height: {synthetic['height_cm']} cm (adjusted from {profile_data['height_cm']})")
            print(f"   Age: {synthetic['age']}")
            print(f"   Interests: {synthetic['interests']}")
            print(f"   Personality: O={synthetic['openness']}, C={synthetic['conscientiousness']}, E={synthetic['extraversion']}, A={synthetic['agreeableness']}, N={synthetic['neuroticism']}")
            print(f"   Personality Tags: {synthetic['personality_tags']}")
            
            print(f"\n📊 Modifications Applied:")
            modifications = result['data']['modifications']
            for key, value in modifications.items():
                print(f"   {key.replace('_', ' ').title()}: {value}")
            
            print(f"\n💾 Full synthetic profile saved to 'synthetic_profile_result.json'")
            
            # Save result to file
            with open('synthetic_profile_result.json', 'w') as f:
                json.dump(result, f, indent=2)
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error!")
        print("Make sure the API server is running:")
        print("   python main.py")
        print("   or")
        print("   python run_server.py")
        
    except requests.exceptions.Timeout:
        print("❌ Request timeout!")
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

def test_multiple_profiles():
    """Test with multiple different profile types."""
    
    profiles = [
        {
            "name": "Young Female Artist",
            "data": {
                "height_cm": 165.0,
                "age": 24,
                "gender": "f",
                "orientation": "straight",
                "body_type": "slim",
                "diet": "vegetarian",
                "drinks": "rarely",
                "smokes": "no",
                "education": "Bachelor's degree",
                "job": "artist / designer",
                "location": "brooklyn, new york",
                "family_plan": "not sure",
                "pets": "likes cats",
                "desired_relationship": "Short-term, open to long-term",
                "openness": 9.0,
                "conscientiousness": 5.0,
                "extraversion": 6.0,
                "agreeableness": 8.0,
                "interests": ["Painting", "Photography", "Yoga", "Coffee", "Museums"]
            }
        },
        {
            "name": "Older Male Professional",
            "data": {
                "height_cm": 182.0,
                "age": 35,
                "gender": "m",
                "orientation": "straight",
                "body_type": "athletic",
                "diet": "mostly anything",
                "drinks": "socially",
                "smokes": "no",
                "education": "Master's degree",
                "job": "finance / banking",
                "location": "manhattan, new york",
                "family_plan": "want kids",
                "pets": "has dogs",
                "desired_relationship": "Long-term relationship",
                "openness": 6.0,
                "conscientiousness": 9.0,
                "extraversion": 8.0,
                "agreeableness": 7.0,
                "neuroticism": 2.0,
                "interests": ["Running", "Wine Tasting", "Travel", "Cooking", "Tennis"]
            }
        }
    ]
    
    print("\n" + "=" * 50)
    print("🧪 Testing Multiple Profile Types")
    print("=" * 50)
    
    for i, profile in enumerate(profiles, 1):
        print(f"\n{i}. {profile['name']}")
        print("-" * 30)
        
        try:
            response = requests.post(
                "http://localhost:8000/create-synthetic-profile",
                json=profile['data'],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                synthetic = result['data']['synthetic_profile']
                print(f"✅ Generated: {profile['data']['gender']} → {synthetic['gender']}")
                print(f"   Height: {profile['data']['height_cm']} → {synthetic['height_cm']} cm")
                print(f"   Interests: {len(profile['data']['interests'])} → {len(synthetic['interests'])}")
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        time.sleep(0.5)  # Small delay between requests

if __name__ == "__main__":
    # Run the main example
    create_synthetic_profile_example()
    
    # Optionally test multiple profiles
    try:
        choice = input("\n🤔 Test multiple profile types? (y/n): ").lower().strip()
        if choice == 'y':
            test_multiple_profiles()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
