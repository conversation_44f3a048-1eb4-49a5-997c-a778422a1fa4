import os
import logging
from typing import Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import magic
from PIL import Image
import io
import base64
import re
import json
import pathlib
import time
import requests
import pandas as pd
import random
import numpy as np

from gcp_storage import GCPStorageClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Image Upload API",
    description="API for uploading images to Google Cloud Storage",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
BUCKET_NAME = "comfyui-data-analytic-project-424703"
FOLDER_PATH = "comfyui/input"
CREDENTIALS_PATH = "gcp-credentials.json"

# Supported image formats
SUPPORTED_IMAGE_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/bmp': ['.bmp'],
    'image/tiff': ['.tiff', '.tif']
}

# Maximum file size (20MB)
MAX_FILE_SIZE = 20 * 1024 * 1024

# Pydantic models
class Base64ImageUpload(BaseModel):
    file: str  # Base64 encoded image data (with or without data URL prefix)

class WorkflowRequest(BaseModel):
    prompt: str  # Description of how to generate the image
    image_url: str  # URL of the input image
    weight: Optional[float] = 0.7  # Weight parameter for PuLID end_at and safety threshold (0.0-1.0)
    cloudrun_url: Optional[str] = "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app"  # ComfyUI Cloud Run URL
    output_node: Optional[str] = "51"  # Output node ID (default to SaveImage node)

class SyntheticProfileRequest(BaseModel):
    # Basic profile information
    height_cm: float
    age: int
    gender: str  # 'm' or 'f'
    orientation: str
    body_type: str
    diet: str
    drinks: str
    smokes: str
    education: str
    job: str
    location: str
    family_plan: str
    pets: str
    desired_relationship: str

    # Personality scores (0-10)
    openness: float
    conscientiousness: float
    extraversion: float
    agreeableness: float
    neuroticism: Optional[float] = None  # Will be randomly generated if not provided

    # Interests (list of strings)
    interests: list[str]

    # Essay (optional)
    essay0: Optional[str] = ""

# Initialize GCP Storage client
try:
    storage_client = GCPStorageClient(
        bucket_name=BUCKET_NAME,
        credentials_path=CREDENTIALS_PATH
    )
    logger.info("GCP Storage client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize GCP Storage client: {str(e)}")
    storage_client = None


def decode_base64_image(base64_data: str) -> tuple[Optional[bytes], Optional[str], str]:
    """
    Decode base64 image data and extract content type.

    Args:
        base64_data: Base64 encoded image (with or without data URL prefix)

    Returns:
        Tuple of (image_bytes, content_type, error_message)
    """
    try:
        # Handle data URL format: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
        if base64_data.startswith('data:'):
            # Extract content type and base64 data
            match = re.match(r'data:([^;]+);base64,(.+)', base64_data)
            if not match:
                return None, None, "Invalid data URL format"

            content_type = match.group(1)
            base64_content = match.group(2)
        else:
            # Plain base64 data - assume PNG as default
            content_type = "image/png"
            base64_content = base64_data

        # Validate content type
        if content_type not in SUPPORTED_IMAGE_TYPES:
            return None, None, f"Unsupported content type: {content_type}"

        # Decode base64
        try:
            image_bytes = base64.b64decode(base64_content)
        except Exception as e:
            return None, None, f"Invalid base64 encoding: {str(e)}"

        # Validate image size
        if len(image_bytes) > MAX_FILE_SIZE:
            return None, None, f"Image size ({len(image_bytes)} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"

        if len(image_bytes) == 0:
            return None, None, "Image data is empty"

        # Validate image content
        try:
            image = Image.open(io.BytesIO(image_bytes))
            image.verify()
            logger.info(f"Base64 image validation successful: {image.format}, {image.size}")
        except Exception as e:
            return None, None, f"Invalid image data: {str(e)}"

        return image_bytes, content_type, ""

    except Exception as e:
        return None, None, f"Error processing base64 image: {str(e)}"


def execute_workflow_api(image_url: str, input_prompt: str, weight: float, cloudrun_url: str, output_node: str) -> dict:
    """
    Execute ComfyUI workflow with given parameters.

    Args:
        image_url: URL of the input image
        input_prompt: Text prompt for image generation
        weight: Weight parameter for PuLID end_at and safety threshold (0.0-1.0)
        cloudrun_url: ComfyUI Cloud Run service URL
        output_node: Output node ID to retrieve results from

    Returns:
        Dictionary containing output URL and execution metadata
    """
    try:
        # Load workflow template
        workflow_path = pathlib.Path(__file__).parent / "workflow.json"

        if not workflow_path.exists():
            raise FileNotFoundError(f"Workflow file not found: {workflow_path}")

        with open(workflow_path) as f:
            workflow = json.load(f)

        # Update workflow with input parameters
        # Node 22 is the positive prompt encoder
        enhanced_prompt = input_prompt + " blur street background, detailed skin, realistic skin texture, dramatic, cinematic, dof, 8k uhd, dslr, high quality"
        workflow["22"]["inputs"]["text"] = enhanced_prompt

        # Node 12 is the image loader
        workflow["12"]["inputs"]["image"] = image_url.split("/")[-1]

        # Node 33 is the PuLID application - set end_at weight
        workflow["33"]["inputs"]["weight"] = weight


        logger.info(f"Executing workflow with prompt: {enhanced_prompt}")
        logger.info(f"Using image URL: {image_url}")
        logger.info(f"Using weight: {weight}")

        # Prepare request payload
        prompt_payload = {"prompt": workflow}

        # Execute workflow
        start_time = time.time()
        response = requests.post(f"{cloudrun_url}/prompt", json=prompt_payload, timeout=1800)  # 30 minute timeout
        execution_time = time.time() - start_time

        response.raise_for_status()

        # Parse response
        result = response.json()
        logger.info(f"Workflow response: {result}")

        # Extract output
        output_node = "51"

        output = result.get("outputs", {}).get(output_node, None)
        print(f"Output for node '{output_node}': {output}")

        filename = output["images"][0]["filename"]
        output_url = f"{cloudrun_url}/view?filename={filename}"

        return {
            "success": True,
            "output_url": output_url,
            "execution_time_seconds": round(execution_time, 2),
            "filename": filename,
            "prompt_used": enhanced_prompt,
            "input_image_url": image_url,
            "weight_used": weight,
            "output_node": output_node
        }

    except requests.exceptions.Timeout:
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Workflow execution timed out (5 minutes)"
        )
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Error communicating with ComfyUI service: {str(e)}"
        )
    except FileNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error in workflow execution: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Workflow execution failed: {str(e)}"
        )


def validate_image_file(file: UploadFile) -> tuple[bool, str]:
    """
    Validate uploaded file is a valid image.
    
    Args:
        file: Uploaded file object
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check file size
        file.file.seek(0, 2)  # Seek to end
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning
        
        if file_size > MAX_FILE_SIZE:
            return False, f"File size ({file_size} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"
        
        if file_size == 0:
            return False, "File is empty"
        
        # Check content type
        if file.content_type not in SUPPORTED_IMAGE_TYPES:
            return False, f"Unsupported file type: {file.content_type}. Supported types: {list(SUPPORTED_IMAGE_TYPES.keys())}"
        
        # Validate file content by trying to open it as an image
        file_content = file.file.read()
        file.file.seek(0)  # Reset for later use
        
        try:
            image = Image.open(io.BytesIO(file_content))
            image.verify()  # Verify it's a valid image
            logger.info(f"Image validation successful: {image.format}, {image.size}")
        except Exception as e:
            return False, f"Invalid image file: {str(e)}"
        
        # Additional MIME type validation using python-magic
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            if mime_type not in SUPPORTED_IMAGE_TYPES:
                return False, f"File content doesn't match expected image type. Detected: {mime_type}"
        except Exception as e:
            logger.warning(f"Could not validate MIME type with python-magic: {str(e)}")
        
        return True, ""
        
    except Exception as e:
        return False, f"Error validating file: {str(e)}"


def generate_synthetic_profile(profile_data: SyntheticProfileRequest) -> dict:
    """
    Generate a synthetic profile based on input profile data.

    Args:
        profile_data: Input profile information

    Returns:
        Dictionary containing the synthetic profile data
    """
    try:
        # Create synthetic profile by modifying the input data
        synthetic_profile = profile_data.dict()

        # Flip gender and adjust height
        if profile_data.gender == 'm':
            synthetic_profile['gender'] = 'f'
            synthetic_profile['height_cm'] = profile_data.height_cm - 5
        else:
            synthetic_profile['gender'] = 'm'
            synthetic_profile['height_cm'] = profile_data.height_cm + 5

        # Load hobbies from JSON file
        hobbies_file_path = pathlib.Path(__file__).parent / "files" / "hobby.json"
        if not hobbies_file_path.exists():
            raise FileNotFoundError(f"Hobbies file not found: {hobbies_file_path}")

        with open(hobbies_file_path, 'r', encoding='utf-8') as f:
            hobbies_data = json.load(f)

        hobbies_list = [hobby['name'] for hobby in hobbies_data]

        # Generate synthetic interests
        # Keep 3 out of 5 original interests (or all if less than 5)
        original_interests = profile_data.interests
        num_to_keep = min(3, len(original_interests))
        selected_interests = random.sample(original_interests, num_to_keep) if len(original_interests) > 0 else []

        # Add 2 new random hobbies
        available_hobbies = [h for h in hobbies_list if h not in selected_interests]
        num_new_interests = min(2, len(available_hobbies))
        new_interests = random.sample(available_hobbies, num_new_interests) if num_new_interests > 0 else []

        synthetic_profile['interests'] = selected_interests + new_interests

        # Generate synthetic personality scores
        personality_columns = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']

        # Generate neuroticism if not provided
        if profile_data.neuroticism is None:
            synthetic_profile['neuroticism'] = np.random.normal(4.5, 1)

        # Add random noise to personality traits (except neuroticism which is already set)
        edit_columns = ['openness', 'conscientiousness', 'extraversion', 'agreeableness']
        for col in edit_columns:
            if col in synthetic_profile:
                synthetic_profile[col] += np.random.normal(loc=0, scale=2)

        # Clip and round personality values to stay within 0-10 range
        for col in personality_columns:
            if col in synthetic_profile:
                synthetic_profile[col] = max(0, min(10, round(synthetic_profile[col])))

        # Generate personality tags
        personality_tags_data = {
            'High': ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive'],
            'Medium': ['Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive'],
            'Low': ['Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']
        }

        index_labels = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']

        # Generate personality tags based on scores
        personality_tags = []
        for i, trait in enumerate(index_labels):
            if trait in synthetic_profile:
                score = synthetic_profile[trait]
                if score <= 3:
                    tag_category = 'Low'
                elif score <= 7:
                    tag_category = 'Medium'
                else:
                    tag_category = 'High'
                personality_tags.append(personality_tags_data[tag_category][i])

        synthetic_profile['personality_tags'] = personality_tags

        # Add execution timestamp
        synthetic_profile['generated_at'] = time.time()

        return synthetic_profile

    except Exception as e:
        logger.error(f"Error generating synthetic profile: {str(e)}")
        raise


@app.get("/")
async def root():
    """API information endpoint."""
    return {
        "message": "Image Upload & ComfyUI Workflow API is running",
        "version": "1.0.0",
        "endpoints": {
            "upload_file": "POST /upload-image (multipart/form-data)",
            "upload_base64": "POST /upload-image-base64 (JSON)",
            "execute_workflow": "POST /execute-workflow (JSON)",
            "create_synthetic_profile": "POST /create-synthetic-profile (JSON)",
            "delete_image": "DELETE /delete-image/{filename}",
            "health": "GET /health",
            "docs": "GET /docs"
        },
        "storage": {
            "bucket": BUCKET_NAME,
            "folder": FOLDER_PATH,
            "supported_formats": list(SUPPORTED_IMAGE_TYPES.keys()),
            "max_file_size_mb": MAX_FILE_SIZE / (1024 * 1024)
        },
        "workflow": {
            "default_cloudrun_url": "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app",
            "default_output_node": "51",
            "default_weight": 0.7,
            "weight_range": "0.0 - 1.0 (controls PuLID strength and safety threshold)"
        }
    }


@app.get("/health")
async def health_check():
    """Detailed health check including GCP connectivity."""
    health_status = {
        "status": "healthy",
        "gcp_storage": "connected" if storage_client else "disconnected",
        "bucket": BUCKET_NAME,
        "folder": FOLDER_PATH
    }
    
    if not storage_client:
        health_status["status"] = "unhealthy"
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=health_status
        )
    
    return health_status


@app.post("/upload-image")
async def upload_image(file: UploadFile = File(...)):
    """
    Upload an image file to Google Cloud Storage.
    
    Args:
        file: Image file to upload
        
    Returns:
        JSON response with upload status and public URL
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )
        
        # Validate the uploaded file
        is_valid, error_message = validate_image_file(file)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )
        
        # Generate unique filename with folder path
        unique_filename = storage_client.generate_unique_filename(
            original_filename=file.filename,
            folder_path=FOLDER_PATH
        )
        
        logger.info(f"Uploading file: {file.filename} -> {unique_filename}")
        
        # Upload file to GCS
        public_url = storage_client.upload_file_from_memory(
            file_obj=file.file,
            destination_blob_name=unique_filename,
            content_type=file.content_type,
            make_public=True
        )
        
        if not public_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload file to Google Cloud Storage"
            )
        
        # Get file info
        file.file.seek(0, 2)
        file_size = file.file.tell()
        
        response_data = {
            "success": True,
            "message": "Image uploaded successfully",
            "data": {
                "original_filename": file.filename,
                "uploaded_filename": unique_filename,
                "public_url": public_url,
                "content_type": file.content_type,
                "file_size_bytes": file_size,
                "bucket": BUCKET_NAME,
                "folder": FOLDER_PATH
            }
        }
        
        logger.info(f"Successfully uploaded {file.filename} to {public_url}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/upload-image-base64")
async def upload_image_base64(data: Base64ImageUpload):
    """
    Upload an image from base64 encoded data to Google Cloud Storage.

    Args:
        data: JSON body containing base64 encoded image

    Returns:
        JSON response with upload status and public URL
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )

        # Decode and validate base64 image
        image_bytes, content_type, error_message = decode_base64_image(data.file)
        if not image_bytes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )

        # Generate unique filename with folder path
        # Extract file extension from content type
        file_extension = SUPPORTED_IMAGE_TYPES.get(content_type, ['.png'])[0]
        original_filename = f"uploaded_image{file_extension}"

        unique_filename = storage_client.generate_unique_filename(
            original_filename=original_filename,
            folder_path=FOLDER_PATH
        )

        logger.info(f"Uploading base64 image: {original_filename} -> {unique_filename}")

        # Create file-like object from bytes
        image_file = io.BytesIO(image_bytes)

        # Upload file to GCS
        public_url = storage_client.upload_file_from_memory(
            file_obj=image_file,
            destination_blob_name=unique_filename,
            content_type=content_type,
            make_public=True
        )

        if not public_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload image to Google Cloud Storage"
            )

        response_data = {
            "success": True,
            "message": "Image uploaded successfully",
            "data": {
                "original_filename": original_filename,
                "uploaded_filename": unique_filename,
                "public_url": public_url,
                "content_type": content_type,
                "file_size_bytes": len(image_bytes),
                "bucket": BUCKET_NAME,
                "folder": FOLDER_PATH
            }
        }

        logger.info(f"Successfully uploaded base64 image to {public_url}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during base64 image upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/execute-workflow")
def execute_workflow_endpoint(request: WorkflowRequest):
    """
    Execute ComfyUI workflow with given prompt and image URL.

    Args:
        request: Workflow request containing prompt, image_url, and optional parameters

    Returns:
        JSON response with workflow execution results and timing
    """
    try:
        logger.info(f"Executing workflow with prompt: '{request.prompt}' and image: '{request.image_url}'")

        # Validate inputs
        if not request.prompt.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Prompt cannot be empty"
            )

        if not request.image_url.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Image URL cannot be empty"
            )

        # Validate image URL format
        if not (request.image_url.startswith('http://') or request.image_url.startswith('https://')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Image URL must be a valid HTTP/HTTPS URL"
            )

        # Validate weight parameter
        if not (0.0 <= request.weight <= 1.0):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Weight must be between 0.0 and 1.0"
            )

        # Execute workflow
        start_time = time.time()
        result = execute_workflow_api(
            image_url=request.image_url,
            input_prompt=request.prompt,
            weight=request.weight,
            cloudrun_url=request.cloudrun_url,
            output_node=request.output_node
        )
        total_time = time.time() - start_time

        # Prepare response
        response_data = {
            "success": True,
            "message": "Workflow executed successfully",
            "data": {
                "output_url": result["output_url"],
                "execution_time_seconds": result["execution_time_seconds"],
                "total_api_time_seconds": round(total_time, 2),
                "filename": result["filename"],
                "input": {
                    "prompt": request.prompt,
                    "image_url": request.image_url,
                    "weight": request.weight,
                    "cloudrun_url": request.cloudrun_url,
                    "output_node": request.output_node
                },
                "workflow_details": {
                    "enhanced_prompt": result["prompt_used"],
                    "input_image_url": result["input_image_url"],
                    "weight_used": result["weight_used"],
                    "output_node_used": result["output_node"]
                }
            }
        }

        logger.info(f"Workflow completed in {total_time:.2f} seconds. Output: {result['output_url']}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in workflow endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.delete("/delete-image/{filename}")
async def delete_image(filename: str):
    """
    Delete an image from Google Cloud Storage.
    
    Args:
        filename: Name of the file to delete (including folder path)
        
    Returns:
        JSON response with deletion status
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )
        
        # Ensure filename includes the folder path
        if not filename.startswith(FOLDER_PATH):
            filename = f"{FOLDER_PATH}/{filename}"
        
        logger.info(f"Deleting file: {filename}")
        
        # Delete file from GCS
        success = storage_client.delete_file(filename)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found or could not be deleted: {filename}"
            )
        
        response_data = {
            "success": True,
            "message": "Image deleted successfully",
            "data": {
                "deleted_filename": filename,
                "bucket": BUCKET_NAME
            }
        }
        
        logger.info(f"Successfully deleted {filename}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file deletion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/create-synthetic-profile")
def create_synthetic_profile_endpoint(request: SyntheticProfileRequest):
    """
    Create a synthetic profile based on input profile data.

    This endpoint takes profile information from the request and generates a synthetic profile
    using the same logic as the original synthetic_profile_matching.py script, but without
    reading from CSV files.

    Args:
        request: Profile data including basic info, personality scores, and interests

    Returns:
        JSON response with the generated synthetic profile
    """
    try:
        logger.info("Creating synthetic profile from request data")

        # Validate input data
        if not request.interests or len(request.interests) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one interest must be provided"
            )

        if request.gender not in ['m', 'f']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Gender must be 'm' or 'f'"
            )

        # Validate personality scores
        personality_fields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness']
        for field in personality_fields:
            value = getattr(request, field)
            if not (0 <= value <= 10):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"{field} must be between 0 and 10"
                )

        if request.neuroticism is not None and not (0 <= request.neuroticism <= 10):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="neuroticism must be between 0 and 10"
            )

        # Generate synthetic profile
        start_time = time.time()
        synthetic_profile = generate_synthetic_profile(request)
        generation_time = time.time() - start_time

        # Prepare response
        response_data = {
            "success": True,
            "message": "Synthetic profile created successfully",
            "data": {
                "synthetic_profile": synthetic_profile,
                "generation_time_seconds": round(generation_time, 3),
                "original_input": {
                    "gender": request.gender,
                    "height_cm": request.height_cm,
                    "age": request.age,
                    "interests_count": len(request.interests),
                    "original_interests": request.interests
                },
                "modifications": {
                    "gender_flipped": f"{request.gender} -> {synthetic_profile['gender']}",
                    "height_adjusted": f"{request.height_cm} -> {synthetic_profile['height_cm']}",
                    "interests_modified": f"{len(request.interests)} -> {len(synthetic_profile['interests'])}",
                    "personality_randomized": True
                }
            }
        }

        logger.info(f"Successfully created synthetic profile in {generation_time:.3f} seconds")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during synthetic profile creation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
