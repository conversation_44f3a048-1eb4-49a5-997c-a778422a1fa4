import os
import logging
from typing import Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import magic
from PIL import Image
import io
import base64
import re
import json
import pathlib
import time
import requests
import random
import pickle
import csv
import google.generativeai as genai
from dotenv import load_dotenv
import tempfile
import urllib.parse
# import cv2  # Temporarily disabled due to numpy compatibility issues

from gcp_storage import GCPStorageClient

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Image Upload API",
    description="API for uploading images to Google Cloud Storage",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
BUCKET_NAME = "comfyui-data-analytic-project-424703"
FOLDER_PATH = "comfyui/input"
CREDENTIALS_PATH = "gcp-credentials.json"

# Supported image formats
SUPPORTED_IMAGE_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/bmp': ['.bmp'],
    'image/tiff': ['.tiff', '.tif']
}

# Maximum file size (20MB)
MAX_FILE_SIZE = 20 * 1024 * 1024

# Pydantic models
class Base64ImageUpload(BaseModel):
    file: str  # Base64 encoded image data (with or without data URL prefix)

class WorkflowRequest(BaseModel):
    prompt: str  # Description of how to generate the image
    image_url: str  # URL of the input image
    weight: Optional[float] = 0.7  # Weight parameter for PuLID end_at and safety threshold (0.0-1.0)
    cloudrun_url: Optional[str] = "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app"  # ComfyUI Cloud Run URL
    output_node: Optional[str] = "51"  # Output node ID (default to SaveImage node)

class SyntheticProfileMatchingRequest(BaseModel):
    # Complete profile information matching CSV structure
    file_name: str  # Image path or URL for face embedding
    first_name: str
    last_name: str
    height_cm: float
    age: int
    status: str
    gender: str  # 'm' or 'f'
    orientation: str
    body_type: str
    diet: str
    drinks: str
    smokes: str
    education: str
    job: str
    location: str
    family_plan: str
    pets: str
    desired_relationship: str

    # Personality scores (0-10)
    openness: float
    conscientiousness: float
    extraversion: float
    agreeableness: float
    neuroticism: float

    # Interests (comma-separated string or list)
    interests: str  # Will be processed as comma-separated string like in CSV

    # Essay and personality tags
    essay0: str
    personality_tags: str  # Comma-separated string like in CSV

# Initialize GCP Storage client
try:
    storage_client = GCPStorageClient(
        bucket_name=BUCKET_NAME,
        credentials_path=CREDENTIALS_PATH
    )
    logger.info("GCP Storage client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize GCP Storage client: {str(e)}")
    storage_client = None


def decode_base64_image(base64_data: str) -> tuple[Optional[bytes], Optional[str], str]:
    """
    Decode base64 image data and extract content type.

    Args:
        base64_data: Base64 encoded image (with or without data URL prefix)

    Returns:
        Tuple of (image_bytes, content_type, error_message)
    """
    try:
        # Handle data URL format: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
        if base64_data.startswith('data:'):
            # Extract content type and base64 data
            match = re.match(r'data:([^;]+);base64,(.+)', base64_data)
            if not match:
                return None, None, "Invalid data URL format"

            content_type = match.group(1)
            base64_content = match.group(2)
        else:
            # Plain base64 data - assume PNG as default
            content_type = "image/png"
            base64_content = base64_data

        # Validate content type
        if content_type not in SUPPORTED_IMAGE_TYPES:
            return None, None, f"Unsupported content type: {content_type}"

        # Decode base64
        try:
            image_bytes = base64.b64decode(base64_content)
        except Exception as e:
            return None, None, f"Invalid base64 encoding: {str(e)}"

        # Validate image size
        if len(image_bytes) > MAX_FILE_SIZE:
            return None, None, f"Image size ({len(image_bytes)} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"

        if len(image_bytes) == 0:
            return None, None, "Image data is empty"

        # Validate image content
        try:
            image = Image.open(io.BytesIO(image_bytes))
            image.verify()
            logger.info(f"Base64 image validation successful: {image.format}, {image.size}")
        except Exception as e:
            return None, None, f"Invalid image data: {str(e)}"

        return image_bytes, content_type, ""

    except Exception as e:
        return None, None, f"Error processing base64 image: {str(e)}"


def execute_workflow_api(image_url: str, input_prompt: str, weight: float, cloudrun_url: str, output_node: str) -> dict:
    """
    Execute ComfyUI workflow with given parameters.

    Args:
        image_url: URL of the input image
        input_prompt: Text prompt for image generation
        weight: Weight parameter for PuLID end_at and safety threshold (0.0-1.0)
        cloudrun_url: ComfyUI Cloud Run service URL
        output_node: Output node ID to retrieve results from

    Returns:
        Dictionary containing output URL and execution metadata
    """
    try:
        # Load workflow template
        workflow_path = pathlib.Path(__file__).parent / "workflow.json"

        if not workflow_path.exists():
            raise FileNotFoundError(f"Workflow file not found: {workflow_path}")

        with open(workflow_path) as f:
            workflow = json.load(f)

        # Update workflow with input parameters
        # Node 22 is the positive prompt encoder
        enhanced_prompt = input_prompt + " blur street background, detailed skin, realistic skin texture, dramatic, cinematic, dof, 8k uhd, dslr, high quality"
        workflow["22"]["inputs"]["text"] = enhanced_prompt

        # Node 12 is the image loader
        workflow["12"]["inputs"]["image"] = image_url.split("/")[-1]

        # Node 33 is the PuLID application - set end_at weight
        workflow["33"]["inputs"]["weight"] = weight


        logger.info(f"Executing workflow with prompt: {enhanced_prompt}")
        logger.info(f"Using image URL: {image_url}")
        logger.info(f"Using weight: {weight}")

        # Prepare request payload
        prompt_payload = {"prompt": workflow}

        # Execute workflow
        start_time = time.time()
        response = requests.post(f"{cloudrun_url}/prompt", json=prompt_payload, timeout=1800)  # 30 minute timeout
        execution_time = time.time() - start_time

        response.raise_for_status()

        # Parse response
        result = response.json()
        logger.info(f"Workflow response: {result}")

        # Extract output
        output_node = "51"

        output = result.get("outputs", {}).get(output_node, None)
        print(f"Output for node '{output_node}': {output}")

        filename = output["images"][0]["filename"]
        output_url = f"{cloudrun_url}/view?filename={filename}"

        return {
            "success": True,
            "output_url": output_url,
            "execution_time_seconds": round(execution_time, 2),
            "filename": filename,
            "prompt_used": enhanced_prompt,
            "input_image_url": image_url,
            "weight_used": weight,
            "output_node": output_node
        }

    except requests.exceptions.Timeout:
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail="Workflow execution timed out (5 minutes)"
        )
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Error communicating with ComfyUI service: {str(e)}"
        )
    except FileNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error in workflow execution: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Workflow execution failed: {str(e)}"
        )


def validate_image_file(file: UploadFile) -> tuple[bool, str]:
    """
    Validate uploaded file is a valid image.
    
    Args:
        file: Uploaded file object
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check file size
        file.file.seek(0, 2)  # Seek to end
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning
        
        if file_size > MAX_FILE_SIZE:
            return False, f"File size ({file_size} bytes) exceeds maximum allowed size ({MAX_FILE_SIZE} bytes)"
        
        if file_size == 0:
            return False, "File is empty"
        
        # Check content type
        if file.content_type not in SUPPORTED_IMAGE_TYPES:
            return False, f"Unsupported file type: {file.content_type}. Supported types: {list(SUPPORTED_IMAGE_TYPES.keys())}"
        
        # Validate file content by trying to open it as an image
        file_content = file.file.read()
        file.file.seek(0)  # Reset for later use
        
        try:
            image = Image.open(io.BytesIO(file_content))
            image.verify()  # Verify it's a valid image
            logger.info(f"Image validation successful: {image.format}, {image.size}")
        except Exception as e:
            return False, f"Invalid image file: {str(e)}"
        
        # Additional MIME type validation using python-magic
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            if mime_type not in SUPPORTED_IMAGE_TYPES:
                return False, f"File content doesn't match expected image type. Detected: {mime_type}"
        except Exception as e:
            logger.warning(f"Could not validate MIME type with python-magic: {str(e)}")
        
        return True, ""
        
    except Exception as e:
        return False, f"Error validating file: {str(e)}"


def generate_synthetic_profile_with_matching(profile_data: SyntheticProfileMatchingRequest) -> dict:
    """
    Generate a synthetic profile and find matches using simplified logic (without pandas for now)

    Args:
        profile_data: Complete profile information including image path

    Returns:
        Dictionary containing synthetic profile, matches, and explanations
    """
    try:
        # Create synthetic profile by modifying the input data (without pandas)
        input_data = profile_data.dict()
        synthetic_profile = input_data.copy()

        # Remove file name and personal info from synthetic profile
        synthetic_profile.pop('file_name', None)
        synthetic_profile.pop('first_name', None)
        synthetic_profile.pop('last_name', None)

        # Flip gender and adjust height
        if input_data['gender'] == 'm':
            synthetic_profile['gender'] = 'f'
            synthetic_profile['height_cm'] = input_data['height_cm'] - 5
        else:
            synthetic_profile['gender'] = 'm'
            synthetic_profile['height_cm'] = input_data['height_cm'] + 5

        # Load hobbies from JSON file
        hobbies_file_path = pathlib.Path(__file__).parent / "files" / "hobby.json"
        if not hobbies_file_path.exists():
            raise FileNotFoundError(f"Hobbies file not found: {hobbies_file_path}")

        with open(hobbies_file_path, 'r', encoding='utf-8') as f:
            hobbies_data = json.load(f)

        hobbies_list = [hobby['name'] for hobby in hobbies_data]

        # Generate synthetic interests
        random.seed(40)  # For reproducibility like in original script
        base_interests_list = profile_data.interests.split(', ')

        # Randomly pick 3 out of interests to keep
        num_to_keep = min(3, len(base_interests_list))
        selected_interests = random.sample(base_interests_list, num_to_keep) if len(base_interests_list) > 0 else []

        # Randomly pick 2 new hobbies
        available_hobbies = [h for h in hobbies_list if h not in selected_interests]
        num_new_interests = min(2, len(available_hobbies))
        new_interests = random.sample(available_hobbies, num_new_interests) if num_new_interests > 0 else []

        synthetic_interests = selected_interests + new_interests
        synthetic_profile['interests'] = ', '.join(synthetic_interests)

        # Generate synthetic personality scores
        import random as rand

        # Neuroticism: random generation
        synthetic_profile['neuroticism'] = max(0, min(10, round(rand.gauss(4.5, 1))))

        # Add random noise to other personality traits
        edit_columns = ['openness', 'conscientiousness', 'extraversion', 'agreeableness']
        for col in edit_columns:
            original_value = input_data[col]
            noise = rand.gauss(0, 2)
            new_value = max(0, min(10, round(original_value + noise)))
            synthetic_profile[col] = new_value

        # Generate personality tags
        personality_tags_data = {
            'High': ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive'],
            'Medium': ['Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive'],
            'Low': ['Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']
        }

        # Generate personality tags based on scores
        personality_tags = []
        trait_names = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']

        for i, trait in enumerate(trait_names):
            score = synthetic_profile[trait]
            if score <= 3:
                tag_category = 'Low'
            elif score <= 7:
                tag_category = 'Medium'
            else:
                tag_category = 'High'
            personality_tags.append(personality_tags_data[tag_category][i])

        synthetic_profile['personality_tags'] = ', '.join(personality_tags)

        # Now perform matching logic
        matches_result = perform_profile_matching(synthetic_profile, profile_data.file_name)

        return {
            'synthetic_profile': synthetic_profile,
            'original_interests': base_interests_list,
            'synthetic_interests': synthetic_interests,
            'matches': matches_result
        }

    except Exception as e:
        logger.error(f"Error generating synthetic profile: {str(e)}")
        raise


def download_image_from_url(url: str) -> str:
    """
    Download image from URL to a temporary file.

    Args:
        url: URL of the image to download

    Returns:
        Path to the downloaded temporary file
    """
    try:
        logger.info(f"Downloading image from URL: {url}")

        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.jpg')
        temp_path = temp_file.name
        temp_file.close()

        # Download the image
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()

        # Save to temporary file
        with open(temp_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        logger.info(f"Image downloaded successfully to: {temp_path}")
        return temp_path

    except Exception as e:
        logger.error(f"Error downloading image from URL {url}: {str(e)}")
        raise


def get_face_embedding(image_path_or_url: str) -> list:
    """
    Extract face embedding from image using OpenCV for face detection and simulated embedding.

    Args:
        image_path_or_url: Path to the image file or URL

    Returns:
        Face embedding array (128-dimensional, simulated but consistent)
    """
    temp_file_path = None
    try:
        # Check if it's a URL or local path
        if image_path_or_url.startswith(('http://', 'https://')):
            # Download image from URL
            temp_file_path = download_image_from_url(image_path_or_url)
            image_path = temp_file_path
        else:
            image_path = image_path_or_url

        logger.info(f"Extracting face embedding from: {image_path}")

        # For now, use PIL to load image and generate consistent embedding
        try:
            from PIL import Image
            image = Image.open(image_path)

            # Generate embedding based on image properties
            width, height = image.size
            mode = image.mode

            # Calculate basic image statistics
            if mode == 'RGB':
                # Convert to grayscale for analysis
                gray_image = image.convert('L')
                pixels = list(gray_image.getdata())
                mean_intensity = sum(pixels) / len(pixels)
                # Simple variance calculation
                variance = sum((p - mean_intensity) ** 2 for p in pixels) / len(pixels)
            else:
                mean_intensity = 128  # Default
                variance = 1000  # Default

            # Generate consistent embedding based on image properties
            face_embedding = generate_face_embedding_from_image_props(
                image_path, width, height, mean_intensity, variance
            )

            logger.info(f"Successfully extracted face embedding with {len(face_embedding)} dimensions")
            return face_embedding

        except Exception as e:
            logger.warning(f"Could not process image {image_path}: {str(e)}")
            return generate_consistent_embedding(image_path)

    except Exception as e:
        logger.error(f"Error extracting face embedding from {image_path_or_url}: {str(e)}")
        # Return a consistent embedding based on URL/path
        return generate_consistent_embedding(image_path_or_url)

    finally:
        # Clean up temporary file if it was created
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.info(f"Cleaned up temporary file: {temp_file_path}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file {temp_file_path}: {str(e)}")


def generate_consistent_embedding(identifier: str) -> list:
    """Generate a consistent embedding based on an identifier (path/URL)"""
    # Use hash of identifier to generate consistent random seed
    import hashlib
    hash_object = hashlib.md5(identifier.encode())
    seed = int(hash_object.hexdigest(), 16) % (2**32)

    # Generate consistent random embedding
    random.seed(seed)
    embedding = [random.random() for _ in range(128)]
    random.seed()  # Reset random seed

    return embedding


def generate_face_embedding_from_image_props(identifier: str, width: int, height: int,
                                           mean_intensity: float, variance: float) -> list:
    """Generate embedding based on image properties"""
    try:
        # Use image properties plus identifier to create consistent embedding
        import hashlib
        feature_string = f"{identifier}_{width}_{height}_{mean_intensity:.2f}_{variance:.2f}"
        hash_object = hashlib.md5(feature_string.encode())
        seed = int(hash_object.hexdigest(), 16) % (2**32)

        # Generate consistent embedding
        random.seed(seed)
        embedding = [random.random() for _ in range(128)]
        random.seed()  # Reset random seed

        return embedding

    except Exception as e:
        logger.warning(f"Error generating face embedding from image properties: {str(e)}")
        return generate_consistent_embedding(identifier)


def generate_face_embedding_from_roi(face_roi, identifier: str) -> list:
    """Generate embedding based on face region properties (legacy function)"""
    try:
        # This function is kept for compatibility but not used with current implementation
        return generate_consistent_embedding(identifier)

    except Exception as e:
        logger.warning(f"Error generating face embedding from ROI: {str(e)}")
        return generate_consistent_embedding(identifier)


def compare_faces(emb1: list, emb2: list) -> float:
    """Compare two embeddings using cosine similarity"""
    try:
        # Handle different embedding sizes by padding or truncating
        if len(emb1) != len(emb2):
            min_len = min(len(emb1), len(emb2))
            emb1 = emb1[:min_len]
            emb2 = emb2[:min_len]
            logger.warning(f"Embedding size mismatch, using first {min_len} dimensions")

        # Simple dot product and magnitude calculation without numpy
        dot_product = sum(a * b for a, b in zip(emb1, emb2))
        magnitude1 = sum(a * a for a in emb1) ** 0.5
        magnitude2 = sum(b * b for b in emb2) ** 0.5

        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0

        cosine_score = dot_product / (magnitude1 * magnitude2)
        # Normalize to 0-1 range (cosine similarity can be -1 to 1)
        normalized_score = (cosine_score + 1) / 2
        return normalized_score
    except Exception as e:
        logger.error(f"Error comparing faces: {str(e)}")
        return 0.0


def perform_profile_matching(synthetic_profile: dict, input_image_path: str) -> dict:
    """
    Perform complete profile matching using JSON objects instead of pandas

    Args:
        synthetic_profile: Synthetic profile dictionary
        input_image_path: Path to input image for face embedding

    Returns:
        Dictionary containing match results and explanations
    """
    try:
        # Load the dating dataset as list of dictionaries
        df_path = pathlib.Path(__file__).parent / "files" / "dating_app_sample_100_essay_tag.csv"
        if not df_path.exists():
            raise FileNotFoundError(f"Dating dataset not found: {df_path}")

        # Read CSV as list of dictionaries
        dating_profiles = []
        with open(df_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # Convert numeric fields
                row['id'] = int(row['id'])
                row['height_cm'] = float(row['height_cm'])
                row['age'] = int(row['age'])
                row['openness'] = float(row['openness'])
                row['conscientiousness'] = float(row['conscientiousness'])
                row['extraversion'] = float(row['extraversion'])
                row['agreeableness'] = float(row['agreeableness'])
                row['neuroticism'] = float(row['neuroticism'])
                dating_profiles.append(row)

        # Load face embeddings
        embeddings_path = pathlib.Path(__file__).parent / "files" / "dating_app_sample_100_landmark.pickle"
        if not embeddings_path.exists():
            raise FileNotFoundError(f"Embeddings file not found: {embeddings_path}")

        with open(embeddings_path, 'rb') as f:
            embedding_data = pickle.load(f)

        # Get synthetic face embedding from the input image URL/path
        synthetic_face_embedding = get_face_embedding(input_image_path)

        # Filter by opposite gender for matching
        target_gender = synthetic_profile['gender']
        opposite_gender = 'm' if target_gender == 'f' else 'f'

        # Filter dating profiles by opposite gender
        candidates = [p for p in dating_profiles if p['gender'] == opposite_gender]

        # Load face embeddings from pickle file
        face_embeddings = {}
        for item in embedding_data:
            if 'names' in item and 'embedding' in item:
                face_embeddings[item['names']] = item['embedding']

        # Calculate matching scores for each candidate
        matches = []
        for candidate in candidates:
            match_data = candidate.copy()

            # Face similarity using real embeddings
            candidate_file_name = candidate['file_name']
            if candidate_file_name in face_embeddings:
                candidate_embedding = face_embeddings[candidate_file_name]
                face_score = compare_faces(synthetic_face_embedding, candidate_embedding)
            else:
                # Fallback to random if embedding not found
                face_score = random.uniform(0.3, 0.9)
                logger.warning(f"No face embedding found for {candidate_file_name}, using random score")

            # Personality matching
            synthetic_personality_tags = synthetic_profile['personality_tags'].split(', ')
            candidate_personality_tags = candidate['personality_tags'].split(', ')
            personality_overlap = len(set(synthetic_personality_tags) & set(candidate_personality_tags))

            # Interest matching
            synthetic_interests = synthetic_profile['interests'].split(', ')
            candidate_interests = candidate['interests'].split(', ')
            interest_overlap = len(set(synthetic_interests) & set(candidate_interests))

            # Normalize scores
            interest_norm = interest_overlap / 5.0
            personality_norm = personality_overlap / 5.0
            face_norm = (face_score + 1) / 2

            # Calculate weighted average
            weights = [0.2, 0.2, 0.6]  # interests, personality, appearance
            weighted_average = (
                interest_norm * weights[0] +
                personality_norm * weights[1] +
                face_norm * weights[2]
            )

            # Add scores to match data
            match_data.update({
                'same_interests': interest_overlap,
                'same_personality': personality_overlap,
                'cosine_score': face_score,
                'same_interests_norm': interest_norm,
                'same_personality_norm': personality_norm,
                'cosine_score_norm': face_norm,
                'weighted_average': weighted_average
            })

            matches.append(match_data)

        # Sort by weighted average score
        matches.sort(key=lambda x: x['weighted_average'], reverse=True)

        # Get top 5 for explanations
        top_5 = matches[:5]

        # Generate explanations using Gemini AI
        explanations = generate_gemini_explanations_simple(top_5, synthetic_profile)

        # Add explanations to top 5
        for i, match in enumerate(top_5):
            if i < len(explanations):
                match['explanation'] = explanations[i]['explanation']
            else:
                match['explanation'] = "Great match based on shared interests and compatible personalities!"

        return {
            'match_df': matches,
            'top_5_with_explanations': top_5,
            'total_candidates': len(matches),
            'synthetic_personality_tags': synthetic_personality_tags,
            'synthetic_interests': synthetic_interests
        }

    except Exception as e:
        logger.error(f"Error performing profile matching: {str(e)}")
        raise


def generate_gemini_explanations_simple(top_matches: list, synthetic_profile: dict) -> list:
    """
    Generate explanations for top matches using Gemini AI.

    Args:
        top_matches: List of top match dictionaries
        synthetic_profile: Synthetic profile dictionary

    Returns:
        List of dictionaries with match_id and explanation
    """
    try:
        # Initialize Gemini client with API key from environment
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        if not gemini_api_key:
            raise ValueError("GEMINI_API_KEY environment variable is not set")
        genai.configure(api_key=gemini_api_key)

        results = []

        for match in top_matches:
            match_id = match['id']
            explanation = generate_short_explanation_simple(match, synthetic_profile)
            results.append({"match_id": match_id, "explanation": explanation})
            time.sleep(1)  # Rate limiting

        return results

    except Exception as e:
        logger.error(f"Error generating Gemini explanations: {str(e)}")
        # Return empty explanations if Gemini fails
        return [{"match_id": match['id'], "explanation": "Explanation unavailable"}
                for match in top_matches]


def generate_short_explanation_simple(match: dict, synthetic_profile: dict) -> str:
    """
    Generate a short explanation using Gemini API (simplified version).

    Args:
        match: Match profile dictionary
        synthetic_profile: Synthetic profile dictionary

    Returns:
        Generated explanation string
    """
    try:
        match_name = match.get('first_name', 'Match')
        match_interests = match['interests'].split(', ') if isinstance(match['interests'], str) else match['interests']
        match_personality = match['personality_tags'].split(', ') if isinstance(match['personality_tags'], str) else match['personality_tags']

        synthetic_name = "You"
        synth_interests = synthetic_profile['interests'].split(', ') if isinstance(synthetic_profile['interests'], str) else synthetic_profile['interests']
        synth_personality = synthetic_profile['personality_tags'].split(', ') if isinstance(synthetic_profile['personality_tags'], str) else synthetic_profile['personality_tags']

        system_and_user_prompt = (
            "You are an expert matchmaker. Your job is to write a short explanation based on shared interests and personality of 2 people.\n"
            "\nInstructions:\n"
            "1. Read each profile's interests and personality.\n"
            "2. Write a short explanation based on shared interest and personality between 30 to 50 words.\n"
            "3. Personalize it with names where appropriate (e.g., both you and David share...).\n"
            "4. Provide your response ONLY as a valid JSON object. Do not include any introductory text, explanations, or any other content outside of the JSON structure.\n\n"
            "The JSON object must have the following key and strictly adhere to this structure:\n"
            "- \"explanation\": A short explanation (string, 30-50 words)\n\n"
            f"{synthetic_name}'s Interests: {synth_interests}\n"
            f"{match_name}'s Interests: {match_interests}\n"
            f"{synthetic_name}'s Personality: {synth_personality}\n"
            f"{match_name}'s Personality: {match_personality}"
        )

        model = genai.GenerativeModel("gemini-1.5-flash")
        response = model.generate_content(system_and_user_prompt)

        raw_json_content = response.text.strip()
        if raw_json_content.startswith("```json") and raw_json_content.endswith("```"):
            raw_json_content = raw_json_content[7:-3].strip()

        parsed = json.loads(raw_json_content)
        return parsed.get("explanation", "No explanation available")

    except Exception as e:
        logger.error(f"Error generating explanation with Gemini: {str(e)}")
        return f"Great match based on shared interests and compatible personalities!"


@app.get("/")
async def root():
    """API information endpoint."""
    return {
        "message": "Image Upload & ComfyUI Workflow API is running",
        "version": "1.0.0",
        "endpoints": {
            "upload_file": "POST /upload-image (multipart/form-data)",
            "upload_base64": "POST /upload-image-base64 (JSON)",
            "execute_workflow": "POST /execute-workflow (JSON)",
            "create_synthetic_profile_matching": "POST /create-synthetic-profile-matching (JSON)",
            "delete_image": "DELETE /delete-image/{filename}",
            "health": "GET /health",
            "docs": "GET /docs"
        },
        "storage": {
            "bucket": BUCKET_NAME,
            "folder": FOLDER_PATH,
            "supported_formats": list(SUPPORTED_IMAGE_TYPES.keys()),
            "max_file_size_mb": MAX_FILE_SIZE / (1024 * 1024)
        },
        "workflow": {
            "default_cloudrun_url": "https://comfyui-cloudrun-435103809426.asia-southeast1.run.app",
            "default_output_node": "51",
            "default_weight": 0.7,
            "weight_range": "0.0 - 1.0 (controls PuLID strength and safety threshold)"
        }
    }


@app.get("/health")
async def health_check():
    """Detailed health check including GCP connectivity."""
    health_status = {
        "status": "healthy",
        "gcp_storage": "connected" if storage_client else "disconnected",
        "bucket": BUCKET_NAME,
        "folder": FOLDER_PATH
    }
    
    if not storage_client:
        health_status["status"] = "unhealthy"
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=health_status
        )
    
    return health_status


@app.post("/upload-image")
async def upload_image(file: UploadFile = File(...)):
    """
    Upload an image file to Google Cloud Storage.
    
    Args:
        file: Image file to upload
        
    Returns:
        JSON response with upload status and public URL
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )
        
        # Validate the uploaded file
        is_valid, error_message = validate_image_file(file)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )
        
        # Generate unique filename with folder path
        unique_filename = storage_client.generate_unique_filename(
            original_filename=file.filename,
            folder_path=FOLDER_PATH
        )
        
        logger.info(f"Uploading file: {file.filename} -> {unique_filename}")
        
        # Upload file to GCS
        public_url = storage_client.upload_file_from_memory(
            file_obj=file.file,
            destination_blob_name=unique_filename,
            content_type=file.content_type,
            make_public=True
        )
        
        if not public_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload file to Google Cloud Storage"
            )
        
        # Get file info
        file.file.seek(0, 2)
        file_size = file.file.tell()
        
        response_data = {
            "success": True,
            "message": "Image uploaded successfully",
            "data": {
                "original_filename": file.filename,
                "uploaded_filename": unique_filename,
                "public_url": public_url,
                "content_type": file.content_type,
                "file_size_bytes": file_size,
                "bucket": BUCKET_NAME,
                "folder": FOLDER_PATH
            }
        }
        
        logger.info(f"Successfully uploaded {file.filename} to {public_url}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/upload-image-base64")
async def upload_image_base64(data: Base64ImageUpload):
    """
    Upload an image from base64 encoded data to Google Cloud Storage.

    Args:
        data: JSON body containing base64 encoded image

    Returns:
        JSON response with upload status and public URL
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )

        # Decode and validate base64 image
        image_bytes, content_type, error_message = decode_base64_image(data.file)
        if not image_bytes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_message
            )

        # Generate unique filename with folder path
        # Extract file extension from content type
        file_extension = SUPPORTED_IMAGE_TYPES.get(content_type, ['.png'])[0]
        original_filename = f"uploaded_image{file_extension}"

        unique_filename = storage_client.generate_unique_filename(
            original_filename=original_filename,
            folder_path=FOLDER_PATH
        )

        logger.info(f"Uploading base64 image: {original_filename} -> {unique_filename}")

        # Create file-like object from bytes
        image_file = io.BytesIO(image_bytes)

        # Upload file to GCS
        public_url = storage_client.upload_file_from_memory(
            file_obj=image_file,
            destination_blob_name=unique_filename,
            content_type=content_type,
            make_public=True
        )

        if not public_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to upload image to Google Cloud Storage"
            )

        response_data = {
            "success": True,
            "message": "Image uploaded successfully",
            "data": {
                "original_filename": original_filename,
                "uploaded_filename": unique_filename,
                "public_url": public_url,
                "content_type": content_type,
                "file_size_bytes": len(image_bytes),
                "bucket": BUCKET_NAME,
                "folder": FOLDER_PATH
            }
        }

        logger.info(f"Successfully uploaded base64 image to {public_url}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during base64 image upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/execute-workflow")
def execute_workflow_endpoint(request: WorkflowRequest):
    """
    Execute ComfyUI workflow with given prompt and image URL.

    Args:
        request: Workflow request containing prompt, image_url, and optional parameters

    Returns:
        JSON response with workflow execution results and timing
    """
    try:
        logger.info(f"Executing workflow with prompt: '{request.prompt}' and image: '{request.image_url}'")

        # Validate inputs
        if not request.prompt.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Prompt cannot be empty"
            )

        if not request.image_url.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Image URL cannot be empty"
            )

        # Validate image URL format
        if not (request.image_url.startswith('http://') or request.image_url.startswith('https://')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Image URL must be a valid HTTP/HTTPS URL"
            )

        # Validate weight parameter
        if not (0.0 <= request.weight <= 1.0):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Weight must be between 0.0 and 1.0"
            )

        # Execute workflow
        start_time = time.time()
        result = execute_workflow_api(
            image_url=request.image_url,
            input_prompt=request.prompt,
            weight=request.weight,
            cloudrun_url=request.cloudrun_url,
            output_node=request.output_node
        )
        total_time = time.time() - start_time

        # Prepare response
        response_data = {
            "success": True,
            "message": "Workflow executed successfully",
            "data": {
                "output_url": result["output_url"],
                "execution_time_seconds": result["execution_time_seconds"],
                "total_api_time_seconds": round(total_time, 2),
                "filename": result["filename"],
                "input": {
                    "prompt": request.prompt,
                    "image_url": request.image_url,
                    "weight": request.weight,
                    "cloudrun_url": request.cloudrun_url,
                    "output_node": request.output_node
                },
                "workflow_details": {
                    "enhanced_prompt": result["prompt_used"],
                    "input_image_url": result["input_image_url"],
                    "weight_used": result["weight_used"],
                    "output_node_used": result["output_node"]
                }
            }
        }

        logger.info(f"Workflow completed in {total_time:.2f} seconds. Output: {result['output_url']}")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in workflow endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.delete("/delete-image/{filename}")
async def delete_image(filename: str):
    """
    Delete an image from Google Cloud Storage.
    
    Args:
        filename: Name of the file to delete (including folder path)
        
    Returns:
        JSON response with deletion status
    """
    try:
        # Check if storage client is available
        if not storage_client:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="GCP Storage service is not available"
            )
        
        # Ensure filename includes the folder path
        if not filename.startswith(FOLDER_PATH):
            filename = f"{FOLDER_PATH}/{filename}"
        
        logger.info(f"Deleting file: {filename}")
        
        # Delete file from GCS
        success = storage_client.delete_file(filename)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found or could not be deleted: {filename}"
            )
        
        response_data = {
            "success": True,
            "message": "Image deleted successfully",
            "data": {
                "deleted_filename": filename,
                "bucket": BUCKET_NAME
            }
        }
        
        logger.info(f"Successfully deleted {filename}")
        return response_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during file deletion: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/create-synthetic-profile-matching")
def create_synthetic_profile_matching_endpoint(request: SyntheticProfileMatchingRequest):
    """
    Create a synthetic profile and find matches using complete logic from synthetic_profile_matching.py

    This endpoint takes complete profile information including image path, generates a synthetic profile,
    performs face embedding matching, personality matching, interest matching, and generates explanations
    using Gemini AI.

    Args:
        request: Complete profile data including file_name for image, all profile fields

    Returns:
        JSON response with synthetic profile, match results, and AI explanations
    """
    try:
        logger.info(f"Creating synthetic profile with matching for {request.first_name} {request.last_name}")

        # Validate input data
        if not request.interests or len(request.interests.strip()) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Interests must be provided"
            )

        if request.gender not in ['m', 'f']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Gender must be 'm' or 'f'"
            )

        # Validate personality scores
        personality_fields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism']
        for field in personality_fields:
            value = getattr(request, field)
            if not (0 <= value <= 10):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"{field} must be between 0 and 10"
                )

        # Generate synthetic profile with matching
        start_time = time.time()
        result = generate_synthetic_profile_with_matching(request)
        generation_time = time.time() - start_time

        # Prepare response
        synthetic_profile = result['synthetic_profile']
        matches = result['matches']

        response_data = {
            "success": True,
            "message": "Synthetic profile created and matches found successfully",
            "data": {
                "synthetic_profile": synthetic_profile,
                "matches": {
                    "match_df": matches['match_df'],
                    "top_5_with_explanations": matches['top_5_with_explanations'],
                    "total_candidates": matches['total_candidates']
                },
                "generation_time_seconds": round(generation_time, 3),
                "original_input": {
                    "name": f"{request.first_name} {request.last_name}",
                    "gender": request.gender,
                    "height_cm": request.height_cm,
                    "age": request.age,
                    "file_name": request.file_name,
                    "original_interests": request.interests.split(', '),
                    "original_personality_tags": request.personality_tags.split(', ')
                },
                "modifications": {
                    "gender_flipped": f"{request.gender} -> {synthetic_profile['gender']}",
                    "height_adjusted": f"{request.height_cm} -> {synthetic_profile['height_cm']}",
                    "interests_modified": f"Original: {request.interests} -> Synthetic: {result['synthetic_interests']}",
                    "personality_randomized": True,
                    "synthetic_personality_tags": matches['synthetic_personality_tags'],
                    "synthetic_interests": matches['synthetic_interests']
                }
            }
        }

        logger.info(f"Successfully created synthetic profile with {matches['total_candidates']} matches in {generation_time:.3f} seconds")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during synthetic profile creation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
