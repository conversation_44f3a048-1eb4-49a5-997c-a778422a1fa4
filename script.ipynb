{"cells": [{"cell_type": "code", "execution_count": 3, "id": "2f8cfacc", "metadata": {}, "outputs": [], "source": ["personality = ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive', 'Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive', 'Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']"]}, {"cell_type": "code", "execution_count": 4, "id": "95fc40e6", "metadata": {}, "outputs": [], "source": ["import re\n", "def name_to_code(name):\n", "    \"\"\"\n", "    Convert a hobby name to a code format (uppercase with underscores).\n", "    \n", "    Args:\n", "        name (str): The hobby name\n", "        \n", "    Returns:\n", "        str: The code format (e.g., \"Open-minded\" -> \"OPEN_MINDED\")\n", "    \"\"\"\n", "    # Remove special characters and replace spaces/hyphens with underscores\n", "    code = re.sub(r'[^\\w\\s-]', '', name)\n", "    # Replace spaces and hyphens with underscores\n", "    code = re.sub(r'[\\s-]+', '_', code)\n", "    # Convert to uppercase\n", "    code = code.upper()\n", "    # Remove leading/trailing underscores\n", "    code = code.strip('_')\n", "    return code\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "dce9096c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'Imaginative', 'code': 'IMAGINATIVE'}, {'name': 'Organised', 'code': 'ORGANISED'}, {'name': 'Outgoing', 'code': 'OUTGOING'}, {'name': 'Warm', 'code': 'WARM'}, {'name': 'Sensitive', 'code': 'SENSITIVE'}, {'name': 'Inquisitive', 'code': 'INQUISITIVE'}, {'name': 'Adaptable', 'code': 'ADAPTABLE'}, {'name': 'Friendly', 'code': 'FRIENDLY'}, {'name': 'Considerate', 'code': 'CONSIDERATE'}, {'name': 'Perceptive', 'code': 'PERCEPTIVE'}, {'name': 'Conventional', 'code': 'CONVENTIONAL'}, {'name': 'Spontaneous', 'code': 'SPONTANEOUS'}, {'name': 'Reserved', 'code': 'RESERVED'}, {'name': 'Independent', 'code': 'INDEPENDENT'}, {'name': 'Calm', 'code': 'CALM'}]\n"]}], "source": ["res = []\n", "for x in personality:\n", "    res.append(\n", "        {\"name\": x, \"code\": name_to_code(x)}\n", "    )\n", "    \n", "print(res)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "588f38c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'[{\"name\": \"Imaginative\", \"code\": \"IMAGINATIVE\"}, {\"name\": \"Organised\", \"code\": \"ORGANISED\"}, {\"name\": \"Outgoing\", \"code\": \"OUTGOING\"}, {\"name\": \"Warm\", \"code\": \"WARM\"}, {\"name\": \"Sensitive\", \"code\": \"SENSITIVE\"}, {\"name\": \"Inquisitive\", \"code\": \"INQUISITIVE\"}, {\"name\": \"Adaptable\", \"code\": \"ADAPTABLE\"}, {\"name\": \"Friendly\", \"code\": \"FRIENDLY\"}, {\"name\": \"Considerate\", \"code\": \"CONSIDERATE\"}, {\"name\": \"Perceptive\", \"code\": \"PERCEPTIVE\"}, {\"name\": \"Conventional\", \"code\": \"CONVENTIONAL\"}, {\"name\": \"Spontaneous\", \"code\": \"SPONTANEOUS\"}, {\"name\": \"Reserved\", \"code\": \"RESERVED\"}, {\"name\": \"Independent\", \"code\": \"INDEPENDENT\"}, {\"name\": \"<PERSON>m\", \"code\": \"CALM\"}]'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "x = json.dumps(res)\n", "x"]}, {"cell_type": "code", "execution_count": null, "id": "2725c9d2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}