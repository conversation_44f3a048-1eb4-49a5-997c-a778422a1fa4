# -*- coding: utf-8 -*-
"""Synthetic Profile Modification and Matching.ipynb
"""

from google.colab import drive
drive.mount('/content/drive')

import pandas as pd
import random
import numpy as np
import time
import pickle
import json
import os
import matplotlib.pyplot as plt
from PIL import Image

# from sklearn.metrics.pairwise import euclidean_distances
# from sklearn.preprocessing import MinMaxScaler

"""## Create Synthetic Profile

### Pick a profile
"""

# load data
df = pd.read_csv("./files/dating_add_sample_100_essay_tag.csv")
# pick a random row
df_sample = df.sample(1, random_state=3)
df_sample

"""### The Synthetic image is generated via ComfyUI back-end

If the `df_sample['gender'] = 'f'` then use male prompt and vice versa

### Basic Info of Synthetic Profile

For Profile Synthetic Page
"""

synthetic_df = df_sample.drop(columns = ['file_name','first_name','last_name'])
if df_sample['gender'].values[0] == 'm':
  synthetic_df['gender'] = 'f'
  synthetic_df['height_cm'] = df_sample['height_cm'] - 5
else:
  synthetic_df['gender'] = 'm'
  synthetic_df['height_cm'] = df_sample['height_cm'] + 5
synthetic_df.head()

"""### Synthetic Profile: Interest"""

# Read hobbies from JSON file
import json

with open('files/hobby.json', 'r', encoding='utf-8') as f:
    hobbies_data = json.load(f)

hobbies_list = [hobby['name'] for hobby in hobbies_data]
print(hobbies_list)

random.seed(40)

# base profile interest list
base_interests_list = df_sample['interests'].values[0].split(', ')

# randomly pick 3 out 5 hobbies to keep
selected_interests = random.sample(base_interests_list, 3)

# randomly pick 2 new hobbies
new_interests = random.sample(hobbies_list, 2)

synthetic_interests = selected_interests + new_interests

synthetic_df['interests'] = [synthetic_interests]
synthetic_df

"""### Synthetic Profile: Personality
"""

#@ Edit personality based on above suggestion
personality_columns = ['openness', 'conscientiousness','extraversion', 'agreeableness', 'neuroticism']
# Neuroticism number from 1 to 10
synthetic_df['neuroticism'] = np.random.normal(4.5, 1, size=len(synthetic_df))

# Add random noise (normal distribution with mean=0, std=2) to the edit columns
edit_columns = ['openness', 'conscientiousness','extraversion', 'agreeableness']
for col in edit_columns:
    synthetic_df[col] += np.random.normal(loc=0, scale=2, size=len(synthetic_df))

# Clip and Round values to stay within 0–10 range
synthetic_df[personality_columns] = synthetic_df[personality_columns].clip(lower=0, upper=10)
synthetic_df[personality_columns] = synthetic_df[personality_columns].round()

synthetic_df[personality_columns]

#@title Personality Tag of synthetic profile

data = {
    'High': ['Imaginative', 'Organised', 'Outgoing', 'Warm', 'Sensitive'],
    'Medium': ['Inquisitive', 'Adaptable', 'Friendly', 'Considerate', 'Perceptive'],
    'Low': ['Conventional', 'Spontaneous', 'Reserved', 'Independent', 'Calm']
}

index_labels = ['openness', 'conscientiousness','extraversion', 'agreeableness', 'neuroticism']

personality_tags = pd.DataFrame(data, index=index_labels)
personality_tags

personality_scores = synthetic_df[personality_columns]

# Define the score ranges (bins) and the labels --- Bins: 1-3, 4-7, 8-10
score_bins = [0, 3, 7, 10]
score_labels = ['Low', 'Medium', 'High']
labeled_df = personality_scores.apply(lambda x: pd.cut(x, bins=score_bins, labels=score_labels))
descriptive_tags = pd.DataFrame({
    trait: labeled_df[trait].map(personality_tags.loc[trait])
    for trait in labeled_df.columns
})
synthetic_df['personality_tags'] = descriptive_tags.apply(list, axis=1)
print(synthetic_df['personality_tags'].iloc[0])

"""## Matching

### Embedding of synthetic profile
"""

# !pip install -q onnxruntime insightface # CPU

# from insightface.app import FaceAnalysis
# app = FaceAnalysis(name="buffalo_l", providers=['CPUExecutionProvider'])
# app.prepare(ctx_id=0, det_size=(640, 640))

# import pickle
# import cv2

# image_folder = './file/profile_pic_processed/'
# image_name = image_folder+df_sample['file_name'].values[0]

# image = cv2.imread(image_name)
# rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
# img = np.array(rgb)[:,:,::-1]

# faces = app.get(img)
# faces = sorted(faces, key=lambda x:(x['bbox'][2]-x['bbox'][0])*(x['bbox'][3]-x['bbox'][1]))[-1]  # select largest face (if more than one detected)
# synthetic_face_embedding = faces['embedding']

# save synthetic embedding as pickle
# saved_path = './files/synthetic_face_embedding.pickle'
# with open(saved_path, "wb") as f:
#     f.write(pickle.dumps(synthetic_face_embedding))

"""### Appearance (Cosine Similarity - Higher is more similar)"""

file_path = open( './files/dating_add_sample_100_embedding.pickle', 'rb')
embedding_pickle = pickle.load(file_path)
file_path.close()

# Load embedding of whole sample
embeddings_df = pd.DataFrame(embedding_pickle)
embeddings_df.head()



#@title Cosine similarity

if synthetic_df['gender'].values[0] == 'm':
  face_sub_df =  embeddings_df[embeddings_df['names'].str[0] == 'M'].copy()
else:
  face_sub_df = embeddings_df[embeddings_df['names'].str[0] == 'F'].copy()

def compare_faces(emb1, emb2):
    """Compare two embeddings using cosine similarity"""
    cosine_score = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
    return cosine_score

face_sub_df['cosine_score'] = face_sub_df['embedding'].apply(lambda x: compare_faces(x, synthetic_face_embedding))

# re-order the index by distance, smaller the distance the more similar the face is
face_sub_df = face_sub_df.sort_values(by=['cosine_score'], ascending=False).reset_index(drop=True)
face_sub_df.head()

"""### Personality"""

if synthetic_df['gender'].values[0] == 'm':
  personality_sub_df = df[['id','gender', 'personality_tags']][df['gender'] == 'm'].copy()
else:
  personality_sub_df = df[['id','gender', 'personality_tags']][df['gender'] == 'f'].copy()
personality_sub_df.head()

target_set = set(synthetic_df['personality_tags'].iloc[0])

personality_sub_df['personality_tags'] = personality_sub_df['personality_tags'].apply(lambda x: [i.strip() for i in x.split(',')])

# Function to compute overlap count
def personality_overlap(row_personality):
    row_set = set(row_personality)
    return len(target_set.intersection(row_set))

personality_sub_df['same_personality'] = personality_sub_df['personality_tags'].apply(personality_overlap)

personality_sub_df = personality_sub_df.sort_values(by=['same_personality'], ascending = False).reset_index(drop=True)
personality_sub_df.head()

"""###  Interests"""

if synthetic_df['gender'].values[0] == 'm':
  interest_sub_df = df[['id','gender', 'interests']][df['gender'] == 'm'].copy()
else:
  interest_sub_df = df[['id','gender', 'interests']][df['gender'] == 'f'].copy()

# interest_sub_df.head()

# Convert the single interest list to a set
target_set = set(synthetic_df['interests'].iloc[0])
# target_set = set(synthetic_interests)
interest_sub_df['interests'] = interest_sub_df['interests'].apply(lambda x: [i.strip() for i in x.split(',')])

# Function to compute overlap count
def interest_overlap(row_interests):
    row_set = set(row_interests)
    return len(target_set.intersection(row_set))

interest_sub_df['same_interests'] = interest_sub_df['interests'].apply(interest_overlap)

interest_sub_df = interest_sub_df.sort_values(by=['same_interests'], ascending = False).reset_index(drop=True)
interest_sub_df.head()

"""### Combine 3 scores"""

match_df = pd.merge(interest_sub_df[['id','gender', 'same_interests']], personality_sub_df[['id', 'same_personality']], on='id', how='inner')
match_df = pd.merge(match_df,face_sub_df[['id','cosine_score']], on='id', how = 'inner')

# Normalize to the same scale of 0 to 1

match_df['same_interests_norm'] = (match_df['same_interests'] - 0) / (5 - 0) # Max similar interest is 5, which might not appear in the dataset
match_df['same_personality_norm'] = (match_df['same_personality'] - 0) / (5 - 0)
match_df['cosine_score_norm'] = (match_df['cosine_score'] + 1) /2

# Weighted average score
weights = [0.2, 0.2, 0.6]
match_df['weighted_average'] = (
    match_df['same_interests_norm'] * weights[0] +
    match_df['same_personality_norm'] * weights[1] +
    match_df['cosine_score_norm'] * weights[2]
)
match_df = match_df.sort_values(by=['weighted_average'], ascending = False).reset_index(drop=True)
match_df.head()

# combine to df to get file_name for visualization

match_df = pd.merge(match_df, df[['id', 'file_name']], on='id', how='inner')
match_df.head()

file_name = df_sample['file_name'].values[0].split('.')[0]
match_df.to_csv(f'./files/match_result_{file_name}.csv', index=False)

"""### Gemeni AI for explaination"""

top_5 = match_df.head(5)
top_5 = pd.merge(top_5,df[['id', 'first_name','interests', 'personality_tags']], how='inner', on = 'id' )

from google import genai
from google.colab import userdata
gemini_api = userdata.get('gemini_api')

client = genai.Client(api_key=gemini_api)

# response = client.models.generate_content(
#     model="gemini-2.5-flash", contents="Explain how AI works in a few words"
# )
# print(response.text)

def generate_short_explanation(row, synthetic_row) -> str | None:
    """
    Uses the Gemini API to generate a short explanation based on shared interests and personality traits
    between a match and a synthetic profile.

    Args:
        row (pd.Series): A row from the top matches DataFrame.
        synthetic_row (pd.Series): A single row from the synthetic profile DataFrame.

    Returns:
        str | None: The generated explanation string, or None if an error occurs.
    """

    match_name = row.get('first_name', 'Match') # Use .get for safety
    match_interests = row['interests']
    match_personality = row['personality_tags']

    synthetic_name = synthetic_row.get('first_name', 'You') # Use .get for safety
    synth_interests = synthetic_row['interests']
    synth_personality = synthetic_row['personality_tags']

    # The prompt asks for the 'explanation' in the JSON output
    system_and_user_prompt = (
        "You are an expert matchmaker. Your job is to write a short explanation based on shared interests and personality of 2 people.\n"
        "\nInstructions:\n"
        "1. Read each profile's interests and personality.\n"
        "2. Write a short explanation based on shared interest and personality between 30 to 50 words.\n"
        "3. Personalize it with names where appropriate (e.g., both you and David share...).\n"
        "4. Provide your response ONLY as a valid JSON object. Do not include any introductory text, explanations, or any other content outside of the JSON structure.\n\n"
        "The JSON object must have the following key and strictly adhere to this structure:\n"
        "- \"explanation\": A short explanation (string, 30-50 words)\n\n"
        f"{synthetic_name}'s Interests: {synth_interests}\n"
        f"{match_name}'s Interests: {match_interests}\n"
        f"{synthetic_name}'s Personality: {synth_personality}\n"
        f"{match_name}'s Personality: {match_personality}"
    )

    try:
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=[
                {"role": "user", "parts": [{"text": system_and_user_prompt}]}
            ]
        )

        raw_json_content = response.text
        print(f"Raw content from LLM: {raw_json_content}") # Debug print

        # Clean raw_json_content by stripping whitespace and potential markdown backticks
        cleaned_json_content = raw_json_content.strip()
        if cleaned_json_content.startswith("```json") and cleaned_json_content.endswith("```"):
            cleaned_json_content = cleaned_json_content[7:-3].strip() # Remove ```json and ```

        parsed = json.loads(cleaned_json_content)
        final_explanation = parsed.get("explanation")

        # If explanation is still None, it means the key wasn't found or was empty/null
        if final_explanation is None:
            print(f"WARNING: 'explanation' key not found or was None in parsed JSON: {parsed}")

        return final_explanation # Return only the explanation string

    except json.JSONDecodeError as e:
        print(f"JSON decoding failed: {e}")
        if hasattr(response, 'text'):
            print(f"Response content that caused error: {response.text}")
        else:
            print("Response content not available for JSON decode error.")
        return None # Return None on JSON decoding error
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None # Return None on any other unexpected error


def generate_all_explanation_df(df1: pd.DataFrame, df2: pd.DataFrame) -> list:
    synthetic_row = df2.iloc[0]
    results = []
    for _, row in df1.iterrows():
        match_id = row['id']
        explanation = generate_short_explanation(row, synthetic_row)

        # Construct the result dictionary with match_id and explanation
        results.append({"match_id": match_id, "explanation": explanation})
        time.sleep(5) # Reduced sleep for faster demo, but adjust for rate limits
    return results

explanations = generate_all_explanation_df(top_5, synthetic_df)

explanations_df = pd.DataFrame(explanations)
explanations_df.rename(columns={'match_id': 'id'}, inplace=True)
top_5 = pd.merge(top_5, explanations_df, on='id', how='left')

import matplotlib.pyplot as plt
import pandas as pd
from PIL import Image
import os

image_folder = './files/profile_pic_processed/'

fig, axes = plt.subplots(5, 2, figsize=(10, 20))
axes = axes.ravel()

# Loop through the top 5 matches
for idx, (_, row) in enumerate(top_5.iterrows()):
    # Display Image in the left column (even indices in the flattened axes array)
    img_path = os.path.join(image_folder, row['file_name'])
    try:
        img = Image.open(img_path)
        axes[2 * idx].imshow(img)
        axes[2 * idx].axis('off')
    except Exception as e:
        print(f"Error loading image {row['file_name']}: {e}")
        axes[2 * idx].axis('off')
        axes[2 * idx].set_title(f"Missing: {row['file_name']}", fontsize=8)

    # Display Explanation in the right column (odd indices)
    explanation_text = row['explanation']
    axes[2 * idx + 1].text(0.05, 0.95, explanation_text, horizontalalignment='left', verticalalignment='top', wrap=True, fontsize=10)
    axes[2 * idx + 1].set_title(f"{row['first_name']}  - ID: {row['id']} - Matching Score: {row['weighted_average']:.3f}", fontsize=12)
    axes[2 * idx + 1].axis('off')

fig.suptitle('Top 5 Matches with Explanations', fontsize=16, y=0.95)
plt.tight_layout(rect=[0, 0.03, 1, 0.95])
plt.show()

