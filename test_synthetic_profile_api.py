#!/usr/bin/env python3
"""
Test script for the synthetic profile API endpoint.
"""

import requests
import json

# Test data based on the CSV structure
test_profile_data = {
    "height_cm": 177.8,
    "age": 28,
    "gender": "m",
    "orientation": "straight",
    "body_type": "average",
    "diet": "mostly anything",
    "drinks": "socially",
    "smokes": "no",
    "education": "Bachelor's degree",
    "job": "computer / hardware / software",
    "location": "san francisco, california",
    "family_plan": "want kids",
    "pets": "likes dogs",
    "desired_relationship": "Long-term relationship",
    "openness": 8.0,
    "conscientiousness": 6.0,
    "extraversion": 7.0,
    "agreeableness": 8.0,
    "neuroticism": 3.0,
    "interests": [
        "Pickleball",
        "Stand-Up Comedy", 
        "Taekwondo",
        "Vinyl Records Collecting",
        "Judo"
    ],
    "essay0": "I'm an open book for the most part. I believe in openness and honesty."
}

def test_synthetic_profile_api(base_url="http://localhost:8000"):
    """Test the synthetic profile creation API."""
    
    print("🧪 Testing Synthetic Profile API")
    print("=" * 50)
    
    # Test the API endpoint
    url = f"{base_url}/create-synthetic-profile"
    
    try:
        print(f"📡 Sending request to: {url}")
        print(f"📝 Input profile data:")
        print(f"   - Gender: {test_profile_data['gender']}")
        print(f"   - Height: {test_profile_data['height_cm']} cm")
        print(f"   - Age: {test_profile_data['age']}")
        print(f"   - Interests: {len(test_profile_data['interests'])} items")
        print(f"   - Personality scores: O={test_profile_data['openness']}, C={test_profile_data['conscientiousness']}, E={test_profile_data['extraversion']}, A={test_profile_data['agreeableness']}, N={test_profile_data['neuroticism']}")
        
        response = requests.post(url, json=test_profile_data, timeout=30)
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"⏱️  Generation time: {result['data']['generation_time_seconds']} seconds")
            
            synthetic = result['data']['synthetic_profile']
            print(f"\n🤖 Synthetic Profile Generated:")
            print(f"   - Gender: {synthetic['gender']} (flipped from {test_profile_data['gender']})")
            print(f"   - Height: {synthetic['height_cm']} cm (adjusted from {test_profile_data['height_cm']})")
            print(f"   - Age: {synthetic['age']}")
            print(f"   - Interests: {len(synthetic['interests'])} items: {synthetic['interests']}")
            print(f"   - Personality tags: {synthetic['personality_tags']}")
            print(f"   - Personality scores: O={synthetic['openness']}, C={synthetic['conscientiousness']}, E={synthetic['extraversion']}, A={synthetic['agreeableness']}, N={synthetic['neuroticism']}")
            
            print(f"\n📈 Modifications:")
            mods = result['data']['modifications']
            for key, value in mods.items():
                print(f"   - {key}: {value}")
                
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the API server is running on localhost:8000")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")

def test_api_info(base_url="http://localhost:8000"):
    """Test the root endpoint to see if API is running."""
    
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("🚀 API is running!")
            print(f"📋 Available endpoints: {list(result['endpoints'].keys())}")
            return True
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 Checking if API is running...")
    if test_api_info():
        print("\n" + "=" * 50)
        test_synthetic_profile_api()
    else:
        print("\n💡 To start the API server, run:")
        print("   python main.py")
        print("   or")
        print("   python run_server.py")
