#!/usr/bin/env python3
"""
Test script for the new synthetic profile matching API endpoint.
"""

import requests
import json

# Test data with all required parameters matching the CSV structure
test_profile_data = {
    "file_name": "M_48_image_17.jpg",
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "height_cm": 177.8,
    "age": 48,
    "status": "single",
    "gender": "m",
    "orientation": "straight",
    "body_type": "a little extra",
    "diet": "mostly anything",
    "drinks": "socially",
    "smokes": "no",
    "education": "High school diploma",
    "job": "computer / hardware / software",
    "location": "walnut creek, california",
    "family_plan": "has kids, and might want more",
    "pets": "likes dogs and likes cats",
    "desired_relationship": "Long-term relationship",
    "openness": 8.0,
    "conscientiousness": 6.0,
    "extraversion": 7.0,
    "agreeableness": 8.0,
    "neuroticism": 3.0,
    "interests": "Pickleball, Stand-Up Comedy, Taekwondo, Vinyl <PERSON> Collecting, Judo",
    "essay0": "i'm an open book for the most part. i believe in openness and honesty. that doesn't mean i don't have secrets, however; i just choose who gets to see which pages :)",
    "personality_tags": "Imaginative, Adaptable, Friendly, Warm, Calm"
}

def test_synthetic_profile_matching_api(base_url="http://localhost:8200"):
    """Test the synthetic profile matching API."""
    
    print("🧪 Testing Synthetic Profile Matching API")
    print("=" * 60)
    
    # Test the API endpoint
    url = f"{base_url}/create-synthetic-profile-matching"
    
    try:
        print(f"📡 Sending request to: {url}")
        print(f"📝 Input profile data:")
        print(f"   - Name: {test_profile_data['first_name']} {test_profile_data['last_name']}")
        print(f"   - Gender: {test_profile_data['gender']}")
        print(f"   - Height: {test_profile_data['height_cm']} cm")
        print(f"   - Age: {test_profile_data['age']}")
        print(f"   - Image: {test_profile_data['file_name']}")
        print(f"   - Interests: {test_profile_data['interests']}")
        print(f"   - Personality: O={test_profile_data['openness']}, C={test_profile_data['conscientiousness']}, E={test_profile_data['extraversion']}, A={test_profile_data['agreeableness']}, N={test_profile_data['neuroticism']}")
        
        response = requests.post(url, json=test_profile_data, timeout=60)
        
        print(f"\n📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"⏱️  Generation time: {result['data']['generation_time_seconds']} seconds")
            
            synthetic = result['data']['synthetic_profile']
            matches = result['data']['matches']
            
            print(f"\n🤖 Synthetic Profile Generated:")
            print(f"   - Gender: {synthetic['gender']} (flipped from {test_profile_data['gender']})")
            print(f"   - Height: {synthetic['height_cm']} cm (adjusted from {test_profile_data['height_cm']})")
            print(f"   - Age: {synthetic['age']}")
            print(f"   - Interests: {synthetic['interests']}")
            print(f"   - Personality tags: {synthetic['personality_tags']}")
            
            print(f"\n🎯 Matching Results:")
            print(f"   - Total candidates: {matches['total_candidates']}")
            print(f"   - Top 5 matches with explanations: {len(matches['top_5_with_explanations'])}")
            
            print(f"\n🏆 Top 5 Matches:")
            for i, match in enumerate(matches['top_5_with_explanations'][:5], 1):
                print(f"   {i}. {match.get('first_name', 'Unknown')} (ID: {match['id']})")
                print(f"      - Score: {match['weighted_average']:.3f}")
                print(f"      - Image: {match['file_name']}")
                print(f"      - Interests overlap: {match['same_interests']}")
                print(f"      - Personality overlap: {match['same_personality']}")
                print(f"      - Face similarity: {match['cosine_score']:.3f}")
                if 'explanation' in match:
                    print(f"      - AI Explanation: {match['explanation']}")
                print()
            
            print(f"\n📈 Modifications Applied:")
            mods = result['data']['modifications']
            for key, value in mods.items():
                print(f"   - {key.replace('_', ' ').title()}: {value}")
            
            # Save detailed results
            with open('synthetic_profile_matching_result.json', 'w') as f:
                json.dump(result, f, indent=2)
            print(f"\n💾 Full results saved to 'synthetic_profile_matching_result.json'")
                
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the API server is running on localhost:8000")
        print("Start the server with: python main.py")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long (>60 seconds)")
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")

def test_api_info(base_url="http://localhost:8200"):
    """Test the root endpoint to see if API is running."""
    
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("🚀 API is running!")
            print(f"📋 Available endpoints: {list(result['endpoints'].keys())}")
            return True
        else:
            print(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {str(e)}")
        return False

def test_with_female_profile():
    """Test with a female profile to see gender flipping."""
    
    female_profile = {
        "file_name": "F_25_image_42.jpg",
        "first_name": "Sarah",
        "last_name": "Johnson",
        "height_cm": 165.0,
        "age": 25,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "slim",
        "diet": "vegetarian",
        "drinks": "rarely",
        "smokes": "no",
        "education": "Bachelor's degree",
        "job": "artist / designer",
        "location": "brooklyn, new york",
        "family_plan": "not sure",
        "pets": "likes cats",
        "desired_relationship": "Short-term, open to long-term",
        "openness": 9.0,
        "conscientiousness": 5.0,
        "extraversion": 6.0,
        "agreeableness": 8.0,
        "neuroticism": 4.0,
        "interests": "Painting, Photography, Yoga, Coffee, Museums",
        "essay0": "I'm a creative soul who loves exploring new art forms and meeting interesting people.",
        "personality_tags": "Imaginative, Spontaneous, Friendly, Warm, Perceptive"
    }
    
    print("\n" + "=" * 60)
    print("🧪 Testing with Female Profile")
    print("=" * 60)
    
    try:
        response = requests.post(
            "http://localhost:8200/create-synthetic-profile-matching",
            json=female_profile,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            synthetic = result['data']['synthetic_profile']
            matches = result['data']['matches']
            
            print(f"✅ Female → Male conversion successful!")
            print(f"   Original: {female_profile['gender']} ({female_profile['height_cm']} cm)")
            print(f"   Synthetic: {synthetic['gender']} ({synthetic['height_cm']} cm)")
            print(f"   Found {matches['total_candidates']} male candidates")
            
        else:
            print(f"❌ Failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🔍 Checking if API is running...")
    if test_api_info():
        print("\n" + "=" * 60)
        test_synthetic_profile_matching_api()
        
        # Test with female profile
        try:
            choice = input("\n🤔 Test with female profile too? (y/n): ").lower().strip()
            if choice == 'y':
                test_with_female_profile()
        except KeyboardInterrupt:
            print("\n👋 Test completed!")
    else:
        print("\n💡 To start the API server, run:")
        print("   python main.py")
        print("   or")
        print("   python run_server.py")
        print("\n📋 Make sure you have the required files:")
        print("   - files/dating_add_sample_100_essay_tag.csv")
        print("   - files/dating_add_sample_100_embedding.pickle")
        print("   - files/hobby.json")
