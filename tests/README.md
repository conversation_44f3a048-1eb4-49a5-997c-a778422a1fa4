# Test Suite

This folder contains all test files for the Copula Backend API.

## Test Files

### Core API Tests
- `test_create_synthetic_profile.py` - Tests the new separated synthetic profile creation API
- `test_find_profile_matches.py` - Tests the new separated profile matching API
- `test_separated_apis_workflow.py` - Tests the complete workflow using both separated APIs
- `test_profile_api.py` - Tests the profile data retrieval API endpoints
- `test_synthetic_profile_api.py` - Tests the basic synthetic profile generation API (legacy)
- `test_synthetic_profile_matching_api.py` - Tests the profile matching functionality (legacy)
- `test_face_embedding_api.py` - Tests the face embedding and matching API with real images (legacy)

### Compatibility Tests
- `test_embedding_compatibility.py` - Tests compatibility between API embedding method and database

### Web Interface Tests
- `test_upload.html` - HTML interface for testing file uploads

## Running Tests

### Prerequisites
Make sure the API server is running:
```bash
# From the main directory
docker-compose up -d
# or
python main.py
```

### Run Individual Tests

#### New Separated APIs (Recommended)
```bash
# Test synthetic profile creation (Step 1)
python tests/test_create_synthetic_profile.py

# Test profile matching (Step 2)
python tests/test_find_profile_matches.py

# Test complete workflow (Both steps)
python tests/test_separated_apis_workflow.py
```

#### Legacy Combined APIs
```bash
# Test basic synthetic profile API
python tests/test_synthetic_profile_api.py

# Test profile matching
python tests/test_synthetic_profile_matching_api.py

# Test face embedding with real images
python tests/test_face_embedding_api.py
```

#### Compatibility Tests
```bash
# Test profile data retrieval
python tests/test_profile_api.py

# Test embedding compatibility
python tests/test_embedding_compatibility.py
```

### Test Results
Test results are saved as JSON files in the tests directory:
- `face_embedding_test_result.json` - Results from face embedding tests
- `synthetic_profile_matching_result.json` - Results from matching tests

## API Endpoints Tested

### Health Check
- `GET /health` - API health status

### New Separated APIs (Recommended)
- `POST /create-synthetic-profile` - Create synthetic dating profiles (Step 1)
- `POST /find-profile-matches` - Find matches for synthetic profiles (Step 2)

### Profile Data Retrieval APIs
- `GET /profile/{id}` - Get single profile by ID from CSV database
- `GET /profiles` - Get all profiles with optional pagination

### Legacy Combined API
- `POST /create-synthetic-profile-matching` - Generate profiles with face embedding matching (All-in-one)

## Test Data

The tests use:
- **Real images** from Google Cloud Storage URLs
- **Sample profiles** with realistic demographic data
- **Face embeddings** compatible with InsightFace database format

## Expected Behavior

### Face Embedding API
- Downloads images from URLs
- Processes with InsightFace (or fallback method)
- Generates 512-dimensional embeddings
- Matches against database of 100 dating profiles
- Returns top 5 matches with AI explanations

### Performance Expectations
- Face embedding processing: ~10-15 seconds
- Profile generation: ~2-5 seconds
- Matching algorithm: ~1-3 seconds

## Troubleshooting

### Common Issues
1. **Connection Error**: Make sure API is running on correct port
2. **Timeout**: Face embedding processing can take time, increase timeout
3. **Missing Files**: Ensure all required data files are in `files/` directory

### Required Files
- `files/dating_app_sample_100_essay_tag.csv`
- `files/dating_app_sample_100_embedding.pickle`
- `files/hobby.json`
