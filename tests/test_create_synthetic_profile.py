#!/usr/bin/env python3
"""
Test script for the new create-synthetic-profile API endpoint.
This tests the first part of the separated API - creating synthetic profiles.
"""

import requests
import json
import time

def test_create_synthetic_profile():
    """Test the create-synthetic-profile API endpoint"""
    
    print("🔍 Testing Create Synthetic Profile API")
    print("=" * 50)
    
    # API endpoint
    api_url = "http://localhost:8200/create-synthetic-profile"
    
    # Test data
    test_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>", 
        "height_cm": 165.0,
        "age": 21,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "average",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "student",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs and cats",
        "desired_relationship": "long-term dating",
        "openness": 7.5,
        "conscientiousness": 6.0,
        "extraversion": 8.0,
        "agreeableness": 7.0,
        "neuroticism": 4.0,
        "interests": "Photography, Hiking, Cooking, Reading, Yoga",
        "essay0": "I love exploring new places and trying different cuisines. Always up for an adventure!",
        "personality_tags": "Adventurous, Creative, Friendly, Outgoing, Curious"
    }
    
    try:
        print(f"📤 Sending request to: {api_url}")
        print(f"👤 Creating synthetic profile for: {test_data['first_name']} {test_data['last_name']}")
        print(f"📊 Original gender: {test_data['gender']}, height: {test_data['height_cm']} cm")
        
        start_time = time.time()
        response = requests.post(api_url, json=test_data, timeout=30)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            
            # Extract synthetic profile data
            data = result.get('data', {})
            synthetic_profile = data.get('synthetic_profile', {})
            modifications = data.get('modifications', {})
            
            print(f"\n📋 Synthetic Profile Created:")
            print(f"   🔄 Gender: {modifications.get('gender_flipped', 'Unknown')}")
            print(f"   📏 Height: {modifications.get('height_adjusted', 'Unknown')}")
            print(f"   🎯 Interests: {modifications.get('synthetic_interests', [])}")
            print(f"   🏷️  Personality: {modifications.get('synthetic_personality_tags', [])}")
            
            # Check generation time
            generation_time = result.get('generation_time_seconds', 0)
            print(f"   ⚡ Generation time: {generation_time:.3f} seconds")
            
            # Save the synthetic profile for use in matching test
            synthetic_profile_data = {
                'synthetic_profile': synthetic_profile,
                'image_url': test_data['file_name'],
                'original_data': test_data
            }
            
            with open('tests/synthetic_profile_for_matching.json', 'w') as f:
                json.dump(synthetic_profile_data, f, indent=2)
            
            print(f"\n💾 Synthetic profile saved to 'tests/synthetic_profile_for_matching.json'")
            print(f"   📝 This can be used to test the find-profile-matches API")
            
            return True
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - make sure API is running")
        return False
    except Exception as e:
        print(f"💥 Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_create_synthetic_profile()
    if success:
        print(f"\n🎉 Synthetic profile creation test completed successfully!")
        print(f"\n📋 Next Steps:")
        print(f"   1. ✅ Synthetic profile created")
        print(f"   2. 🔄 Run 'python tests/test_find_profile_matches.py' to test matching")
        print(f"   3. 🔗 Or use both APIs together in your application")
    else:
        print(f"\n❌ Test failed!")
