#!/usr/bin/env python3
"""
Test script to verify face embedding compatibility between the API method and database method.
"""

import sys
import os
import pickle
import numpy as np

# Add parent directory to path to import main module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_embedding_database_structure():
    """Test the structure of the embedding database"""
    print("🔍 Testing Embedding Database Structure")
    print("=" * 50)
    
    try:
        # Load the landmark pickle file
        pickle_path = "../files/dating_app_sample_100_landmark.pickle"
        with open(pickle_path, 'rb') as f:
            data = pickle.load(f)
        
        print(f"✅ Successfully loaded pickle file")
        print(f"📊 Data type: {type(data)}")
        print(f"📊 Data length: {len(data)}")
        
        if isinstance(data, list) and len(data) > 0:
            first_item = data[0]
            print(f"📊 First item type: {type(first_item)}")
            print(f"📊 First item keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not a dict'}")
            
            if isinstance(first_item, dict) and 'embedding' in first_item:
                embedding = first_item['embedding']
                print(f"📊 Embedding type: {type(embedding)}")
                
                if hasattr(embedding, 'shape'):
                    print(f"📊 Embedding shape: {embedding.shape}")
                elif hasattr(embedding, '__len__'):
                    print(f"📊 Embedding length: {len(embedding)}")
                
                # Show a few sample items
                print(f"\n📋 Sample items:")
                for i, item in enumerate(data[:3]):
                    if 'names' in item:
                        emb = item['embedding']
                        emb_len = len(emb) if hasattr(emb, '__len__') else 'Unknown'
                        print(f"   {i+1}. {item['names']} - Embedding length: {emb_len}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading embedding database: {e}")
        return False

def test_api_embedding_method():
    """Test the API embedding method"""
    print("\n🔍 Testing API Embedding Method")
    print("=" * 50)
    
    try:
        # Import the main module to test the embedding function
        from main import get_face_embedding, generate_consistent_embedding
        
        # Test with a sample image path
        test_image_path = "../files/profile_pic_processed/F_21_image_85.png"
        
        if os.path.exists(test_image_path):
            print(f"📸 Testing with image: {test_image_path}")
            embedding = get_face_embedding(test_image_path)
            print(f"✅ Successfully generated embedding")
            print(f"📊 Embedding type: {type(embedding)}")
            print(f"📊 Embedding length: {len(embedding)}")
            print(f"📊 Sample values: {embedding[:5]}...")
            
            return embedding
        else:
            print(f"⚠️  Test image not found: {test_image_path}")
            print("🔄 Testing with consistent embedding fallback")
            embedding = generate_consistent_embedding("test_identifier")
            print(f"✅ Successfully generated consistent embedding")
            print(f"📊 Embedding length: {len(embedding)}")
            return embedding
            
    except Exception as e:
        print(f"❌ Error testing API embedding method: {e}")
        return None

def test_embedding_compatibility():
    """Test compatibility between database and API embeddings"""
    print("\n🔍 Testing Embedding Compatibility")
    print("=" * 50)
    
    # Test database structure
    db_success = test_embedding_database_structure()
    
    # Test API method
    api_embedding = test_api_embedding_method()
    
    if db_success and api_embedding:
        print(f"\n✅ Both methods working!")
        print(f"📊 API embedding dimensions: {len(api_embedding)}")
        print(f"🎯 Compatibility: {'✅ Compatible' if len(api_embedding) == 512 else '⚠️  Dimension mismatch'}")
    else:
        print(f"\n❌ Some tests failed")

if __name__ == "__main__":
    test_embedding_compatibility()
