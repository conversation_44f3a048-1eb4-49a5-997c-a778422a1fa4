#!/usr/bin/env python3
"""
Test script for the updated synthetic profile matching API with real face embedding.
Tests with the provided image URL: https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png
"""

import requests
import json
import time

def test_face_embedding_api():
    """Test the synthetic profile matching API with real face embedding"""
    
    # API endpoint
    api_url = "http://localhost:8200/create-synthetic-profile-matching"
    
    # Test data with the provided image URL
    test_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>", 
        "height_cm": 165.0,
        "age": 21,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "average",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "student",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs and cats",
        "desired_relationship": "long-term dating",
        "openness": 7.5,
        "conscientiousness": 6.0,
        "extraversion": 8.0,
        "agreeableness": 7.0,
        "neuroticism": 4.0,
        "interests": "Photography, Hiking, Cooking, Reading, Yoga",
        "essay0": "I love exploring new places and trying different cuisines. Looking for someone who shares my passion for adventure and good food.",
        "personality_tags": "Creative, Outgoing, Adventurous, Warm, Optimistic"
    }
    
    print("🔍 Testing Face Embedding API with Real Image URL")
    print("=" * 60)
    print(f"📡 Sending request to: {api_url}")
    print(f"🖼️  Image URL: {test_data['file_name']}")
    print(f"👤 Profile: {test_data['first_name']} {test_data['last_name']}")
    print(f"📊 Gender: {test_data['gender']}, Age: {test_data['age']}, Height: {test_data['height_cm']} cm")
    print(f"🎯 Interests: {test_data['interests']}")
    print(f"🧠 Personality: O={test_data['openness']}, C={test_data['conscientiousness']}, E={test_data['extraversion']}, A={test_data['agreeableness']}, N={test_data['neuroticism']}")
    print()
    
    try:
        # Check if API is running
        health_url = "http://localhost:8200/health"
        health_response = requests.get(health_url, timeout=5)
        if health_response.status_code != 200:
            print("❌ API is not running. Please start the API first.")
            return
        
        print("✅ API is running!")
        
        # Send the request
        start_time = time.time()
        response = requests.post(api_url, json=test_data, timeout=120)
        end_time = time.time()
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            print(f"⏱️  Processing time: {end_time - start_time:.3f} seconds")
            
            result = response.json()

            # Extract data from the response
            data = result.get('data', {})

            # Display synthetic profile
            synthetic_profile = data.get('synthetic_profile', {})
            print(f"\n🤖 Synthetic Profile Generated:")
            print(f"   - Gender: {synthetic_profile.get('gender')} (flipped from {test_data['gender']})")
            print(f"   - Height: {synthetic_profile.get('height_cm')} cm (adjusted from {test_data['height_cm']})")
            print(f"   - Age: {synthetic_profile.get('age')}")
            print(f"   - Interests: {synthetic_profile.get('interests')}")
            print(f"   - Personality tags: {synthetic_profile.get('personality_tags')}")

            # Display matching results
            matches = data.get('matches', {})
            match_df = matches.get('match_df', [])
            top_5 = matches.get('top_5_with_explanations', [])
            
            print(f"\n🎯 Matching Results:")
            print(f"   - Total candidates: {matches.get('total_candidates', 0)}")
            print(f"   - Top 5 matches with explanations: {len(top_5)}")
            
            if top_5:
                print(f"\n🏆 Top 5 Matches:")
                for i, match in enumerate(top_5[:5], 1):
                    print(f"   {i}. {match.get('first_name', 'Unknown')} (ID: {match.get('id')})")
                    print(f"      - Score: {match.get('weighted_average', 0):.3f}")
                    print(f"      - Image: {match.get('file_name', 'N/A')}")
                    print(f"      - Interests overlap: {match.get('same_interests', 0)}")
                    print(f"      - Personality overlap: {match.get('same_personality', 0)}")
                    print(f"      - Face similarity: {match.get('cosine_score', 0):.3f}")
                    if 'explanation' in match:
                        print(f"      - AI Explanation: {match['explanation']}")
                    print()
            
            # Display modifications
            original_interests = data.get('original_interests', [])
            synthetic_interests = data.get('synthetic_interests', [])
            
            print(f"📈 Modifications Applied:")
            print(f"   - Gender Flipped: {test_data['gender']} -> {synthetic_profile.get('gender')}")
            print(f"   - Height Adjusted: {test_data['height_cm']} -> {synthetic_profile.get('height_cm')}")
            print(f"   - Interests Modified: Original: {', '.join(original_interests)} -> Synthetic: {synthetic_interests}")
            print(f"   - Personality Randomized: True")
            print(f"   - Real Face Embedding: ✅ Downloaded and processed from URL")
            
            # Save results
            with open('face_embedding_test_result.json', 'w') as f:
                json.dump(result, f, indent=2)
            print(f"\n💾 Full results saved to 'face_embedding_test_result.json'")
            
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out. The API might be processing a large request.")
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error. Make sure the API is running on localhost:8200")
    except Exception as e:
        print(f"💥 Unexpected error: {str(e)}")

if __name__ == "__main__":
    test_face_embedding_api()
