#!/usr/bin/env python3
"""
Test script for the new find-profile-matches API endpoint.
This tests the second part of the separated API - finding matches for synthetic profiles.
"""

import requests
import json
import time
import os

def test_find_profile_matches():
    """Test the find-profile-matches API endpoint"""
    
    print("🔍 Testing Find Profile Matches API")
    print("=" * 50)
    
    # API endpoint
    api_url = "http://localhost:8200/find-profile-matches"
    
    # Try to load synthetic profile from previous test
    synthetic_profile_file = 'tests/synthetic_profile_for_matching.json'
    
    if os.path.exists(synthetic_profile_file):
        print(f"📂 Loading synthetic profile from: {synthetic_profile_file}")
        with open(synthetic_profile_file, 'r') as f:
            saved_data = json.load(f)
        
        synthetic_profile = saved_data['synthetic_profile']
        image_url = saved_data['image_url']
        original_data = saved_data['original_data']
        
        print(f"👤 Using synthetic profile with gender: {synthetic_profile.get('gender', 'Unknown')}")
        print(f"📸 Image URL: {image_url}")
        
    else:
        print(f"⚠️  No saved synthetic profile found. Creating a sample one...")
        # Create a sample synthetic profile for testing
        synthetic_profile = {
            "height_cm": 170.0,
            "age": 21,
            "status": "single",
            "gender": "m",  # Flipped from original 'f'
            "orientation": "straight",
            "body_type": "average",
            "diet": "anything",
            "drinks": "socially",
            "smokes": "no",
            "education": "college/university",
            "job": "student",
            "location": "san francisco, california",
            "family_plan": "wants kids",
            "pets": "likes dogs and cats",
            "desired_relationship": "long-term dating",
            "openness": 6.8,
            "conscientiousness": 7.2,
            "extraversion": 7.5,
            "agreeableness": 6.3,
            "neuroticism": 3.1,
            "interests": "Reading, Photography, Yoga, Kickboxing, Leather crafting",
            "essay0": "I love exploring new places and trying different cuisines. Always up for an adventure!",
            "personality_tags": "Inquisitive, Adaptable, Outgoing, Warm, Perceptive"
        }
        image_url = "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png"
        original_data = {"note": "Sample data for testing"}
    
    # Prepare request data
    test_data = {
        "synthetic_profile": synthetic_profile,
        "image_url": image_url
    }
    
    try:
        print(f"\n📤 Sending request to: {api_url}")
        print(f"🎯 Finding matches for synthetic profile...")
        print(f"   Gender: {synthetic_profile.get('gender', 'Unknown')}")
        print(f"   Age: {synthetic_profile.get('age', 'Unknown')}")
        print(f"   Interests: {synthetic_profile.get('interests', 'Unknown')}")
        
        start_time = time.time()
        response = requests.post(api_url, json=test_data, timeout=120)
        end_time = time.time()
        
        print(f"\n⏱️  Response time: {end_time - start_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            
            # Extract match data
            data = result.get('data', {})
            matches = data.get('matches', [])
            total_candidates = data.get('total_candidates', 0)
            
            print(f"\n🎯 Matching Results:")
            print(f"   📊 Total candidates found: {total_candidates}")
            print(f"   🏆 Top matches returned: {len(matches)}")
            
            # Show top matches
            if matches:
                print(f"\n🏅 Top 5 Matches:")
                for i, match in enumerate(matches[:5], 1):
                    name = match.get('first_name', 'Unknown')
                    score = match.get('total_score', 0)
                    explanation = match.get('explanation', 'No explanation')
                    
                    print(f"   {i}. {name} (Score: {score:.3f})")
                    print(f"      💬 {explanation[:100]}{'...' if len(explanation) > 100 else ''}")
            
            # Check matching time
            matching_time = result.get('matching_time_seconds', 0)
            print(f"\n⚡ Matching time: {matching_time:.3f} seconds")
            
            # Save results
            with open('tests/profile_matches_result.json', 'w') as f:
                json.dump(result, f, indent=2)
            
            print(f"\n💾 Full results saved to 'tests/profile_matches_result.json'")
            
            return True
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out. Face embedding processing can take time.")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - make sure API is running")
        return False
    except Exception as e:
        print(f"💥 Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_find_profile_matches()
    if success:
        print(f"\n🎉 Profile matching test completed successfully!")
        print(f"\n📋 Summary:")
        print(f"   ✅ Profile matching API working")
        print(f"   ✅ Face embedding processing functional")
        print(f"   ✅ AI explanations generated")
        print(f"   ✅ Results saved for review")
    else:
        print(f"\n❌ Test failed!")
