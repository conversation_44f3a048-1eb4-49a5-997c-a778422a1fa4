#!/usr/bin/env python3
"""
Test script to verify that Gemini AI consistently uses the correct format:
"Both you and [name]" or "You and [name] share" instead of first-person references.
"""

import requests
import json
import re
import time

def test_gemini_prompt_consistency():
    """Test that Gemini explanations use consistent second-person format"""
    
    print("🔍 Testing Gemini Prompt Consistency")
    print("=" * 60)
    
    # Test data for profile creation
    test_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>", 
        "height_cm": 165.0,
        "age": 25,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "average",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "teacher",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs and cats",
        "desired_relationship": "long-term dating",
        "openness": 7.5,
        "conscientiousness": 6.0,
        "extraversion": 8.0,
        "agreeableness": 7.0,
        "neuroticism": 4.0,
        "interests": "Photography, Hiking, Cooking, Reading, Yoga",
        "essay0": "I love exploring new places and trying different cuisines.",
        "personality_tags": "Adventurous, Creative, Friendly, Outgoing, Curious"
    }
    
    print(f"👤 Testing with: {test_data['first_name']} {test_data['last_name']}")
    print(f"   Original Gender: {test_data['gender']} (female)")
    
    # Step 1: Create synthetic profile
    print(f"\n🔄 Step 1: Creating Synthetic Profile")
    print("-" * 40)
    
    try:
        profile_response = requests.post(
            "http://localhost:8200/create-synthetic-profile", 
            json=test_data, 
            timeout=30
        )
        
        if profile_response.status_code != 200:
            print(f"❌ Profile creation failed: {profile_response.status_code}")
            return False
        
        profile_result = profile_response.json()
        synthetic_profile = profile_result['data']['synthetic_profile']
        
        print(f"✅ Synthetic profile created")
        print(f"   Synthetic Gender: {synthetic_profile['gender']} (should be 'm')")
        
    except Exception as e:
        print(f"❌ Error in profile creation: {str(e)}")
        return False
    
    # Step 2: Find matches and analyze explanations
    print(f"\n🎯 Step 2: Finding Matches and Analyzing Explanations")
    print("-" * 50)
    
    matching_data = {
        "synthetic_profile": synthetic_profile,
        "image_url": test_data['file_name']
    }
    
    try:
        matching_response = requests.post(
            "http://localhost:8200/find-profile-matches", 
            json=matching_data, 
            timeout=120
        )
        
        if matching_response.status_code != 200:
            print(f"❌ Profile matching failed: {matching_response.status_code}")
            return False
        
        matching_result = matching_response.json()
        matches = matching_result['data']['matches']
        
        print(f"✅ Found {len(matches)} matches")
        
        # Analyze explanations for consistency
        if matches:
            print(f"\n🔍 Analyzing Explanation Consistency:")
            
            correct_patterns = [
                r'\bBoth you and \w+\b',  # "Both you and [name]"
                r'\bYou and \w+ both\b',  # "You and [name] both"
                r'\bYou and \w+ share\b', # "You and [name] share"
                r'\b\w+ and you both\b',  # "[name] and you both"
                r'\b\w+ and you share\b', # "[name] and you share"
            ]
            
            incorrect_patterns = [
                r'\b\w+ and I\b',         # "[name] and I" (WRONG)
                r'\bI and \w+\b',         # "I and [name]" (WRONG)
                r'\bJennifer and I\b',    # "Jennifer and I" (WRONG)
            ]
            
            all_correct = True
            
            for i, match in enumerate(matches[:5], 1):
                name = match.get('first_name', 'Unknown')
                explanation = match.get('explanation', '')
                
                print(f"\n   {i}. {name}:")
                print(f"      💬 \"{explanation}\"")
                
                # Check for correct patterns
                found_correct = False
                for pattern in correct_patterns:
                    if re.search(pattern, explanation, re.IGNORECASE):
                        found_correct = True
                        break
                
                # Check for incorrect patterns
                found_incorrect = False
                for pattern in incorrect_patterns:
                    if re.search(pattern, explanation, re.IGNORECASE):
                        found_incorrect = True
                        print(f"      ❌ FOUND INCORRECT PATTERN: {pattern}")
                        all_correct = False
                        break
                
                if found_correct and not found_incorrect:
                    print(f"      ✅ Correct format used")
                elif not found_correct and not found_incorrect:
                    print(f"      ⚠️  No clear pattern detected (might be okay)")
                else:
                    print(f"      ❌ Incorrect format detected")
                    all_correct = False
            
            # Summary
            print(f"\n📊 Consistency Analysis Results:")
            if all_correct:
                print(f"   🎉 ALL EXPLANATIONS USE CORRECT FORMAT!")
                print(f"   ✅ No first-person references found")
                print(f"   ✅ Consistent use of 'You and [name]' or 'Both you and [name]'")
            else:
                print(f"   ❌ SOME EXPLANATIONS USE INCORRECT FORMAT")
                print(f"   ⚠️  Found first-person references that need fixing")
            
            return all_correct
        else:
            print(f"⚠️  No matches found to analyze")
            return True
        
    except Exception as e:
        print(f"❌ Error in profile matching: {str(e)}")
        return False

def test_specific_name_patterns():
    """Test with specific names that might trigger incorrect patterns"""
    
    print(f"\n" + "=" * 60)
    print("🔍 Testing Specific Name Pattern Edge Cases")
    print("=" * 60)
    
    # Test with a name that might cause issues
    test_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "Isabella",
        "last_name": "Rodriguez", 
        "height_cm": 160.0,
        "age": 28,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "slim",
        "diet": "vegetarian",
        "drinks": "rarely",
        "smokes": "no",
        "education": "masters",
        "job": "designer",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes cats",
        "desired_relationship": "long-term dating",
        "openness": 8.0,
        "conscientiousness": 7.5,
        "extraversion": 6.0,
        "agreeableness": 8.5,
        "neuroticism": 3.0,
        "interests": "Art, Design, Music, Dancing, Travel",
        "essay0": "I'm passionate about creative expression and love discovering new cultures.",
        "personality_tags": "Creative, Thoughtful, Artistic, Empathetic, Curious"
    }
    
    print(f"👤 Testing edge case with: {test_data['first_name']} {test_data['last_name']}")
    
    try:
        # Create synthetic profile
        profile_response = requests.post(
            "http://localhost:8200/create-synthetic-profile", 
            json=test_data, 
            timeout=30
        )
        
        if profile_response.status_code != 200:
            print(f"❌ Profile creation failed")
            return False
        
        synthetic_profile = profile_response.json()['data']['synthetic_profile']
        
        # Quick match test (just get one explanation)
        matching_data = {
            "synthetic_profile": synthetic_profile,
            "image_url": test_data['file_name']
        }
        
        matching_response = requests.post(
            "http://localhost:8200/find-profile-matches", 
            json=matching_data, 
            timeout=60
        )
        
        if matching_response.status_code == 200:
            matches = matching_response.json()['data']['matches']
            if matches:
                explanation = matches[0].get('explanation', '')
                name = matches[0].get('first_name', 'Unknown')
                
                print(f"✅ Sample explanation for {name}:")
                print(f"   💬 \"{explanation}\"")
                
                # Quick check for first-person references
                if re.search(r'\b(I|me|my)\b', explanation, re.IGNORECASE):
                    print(f"   ❌ Found first-person reference!")
                    return False
                else:
                    print(f"   ✅ No first-person references found")
                    return True
            else:
                print(f"⚠️  No matches to test")
                return True
        else:
            print(f"❌ Matching failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Gemini Prompt Consistency Test")
    
    # Test 1: General consistency
    success1 = test_gemini_prompt_consistency()
    
    # Test 2: Edge cases
    success2 = test_specific_name_patterns()
    
    print(f"\n" + "=" * 60)
    print("📋 Gemini Prompt Test Results")
    print("=" * 60)
    
    if success1 and success2:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"✅ Gemini consistently uses correct format")
        print(f"✅ No first-person references found")
        print(f"✅ Proper use of 'You and [name]' or 'Both you and [name]'")
        print(f"\n🔧 Prompt Fix Status: SUCCESSFUL")
        print(f"   The Gemini AI now consistently uses second-person format")
        print(f"   instead of first-person references like 'Jennifer and I'")
    else:
        print(f"❌ SOME TESTS FAILED!")
        print(f"{'✅' if success1 else '❌'} General consistency: {'PASSED' if success1 else 'FAILED'}")
        print(f"{'✅' if success2 else '❌'} Edge case testing: {'PASSED' if success2 else 'FAILED'}")
        print(f"\n⚠️  The prompt may need further refinement")
