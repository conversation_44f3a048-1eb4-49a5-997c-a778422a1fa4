#!/usr/bin/env python3
"""
Test script to verify the gender flipping bug has been fixed.
This test specifically checks that gender is only flipped once, not twice.
"""

import requests
import json
import time

def test_gender_fix():
    """Test that gender is correctly flipped only once in the workflow"""
    
    print("🔍 Testing Gender Fix")
    print("=" * 50)
    
    # Test with female input - should create male synthetic profile and match with males
    test_data_female = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>", 
        "height_cm": 165.0,
        "age": 21,
        "status": "single",
        "gender": "f",  # Original: female
        "orientation": "straight",
        "body_type": "average",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "student",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs and cats",
        "desired_relationship": "long-term dating",
        "openness": 7.5,
        "conscientiousness": 6.0,
        "extraversion": 8.0,
        "agreeableness": 7.0,
        "neuroticism": 4.0,
        "interests": "Photography, Hiking, Cooking, Reading, Yoga",
        "essay0": "I love exploring new places and trying different cuisines.",
        "personality_tags": "Adventurous, Creative, Friendly, Outgoing, Curious"
    }
    
    print(f"📊 Original Profile:")
    print(f"   👤 Name: {test_data_female['first_name']} {test_data_female['last_name']}")
    print(f"   ⚧️  Original Gender: {test_data_female['gender']} (female)")
    print(f"   📏 Height: {test_data_female['height_cm']} cm")
    
    # Step 1: Create synthetic profile
    print(f"\n🔄 Step 1: Creating Synthetic Profile")
    print("-" * 40)
    
    try:
        profile_response = requests.post(
            "http://localhost:8200/create-synthetic-profile", 
            json=test_data_female, 
            timeout=30
        )
        
        if profile_response.status_code != 200:
            print(f"❌ Profile creation failed: {profile_response.status_code}")
            return False
        
        profile_result = profile_response.json()
        synthetic_profile = profile_result['data']['synthetic_profile']
        
        print(f"✅ Synthetic profile created")
        print(f"   ⚧️  Synthetic Gender: {synthetic_profile['gender']} (should be 'm' - male)")
        print(f"   📏 Synthetic Height: {synthetic_profile['height_cm']} cm")
        
        # Verify gender was flipped correctly
        expected_gender = 'm'  # Female should flip to male
        if synthetic_profile['gender'] != expected_gender:
            print(f"❌ ERROR: Expected gender '{expected_gender}', got '{synthetic_profile['gender']}'")
            return False
        
        print(f"✅ Gender flip correct: f → m")
        
    except Exception as e:
        print(f"❌ Error in profile creation: {str(e)}")
        return False
    
    # Step 2: Find matches and verify gender matching
    print(f"\n🎯 Step 2: Finding Profile Matches")
    print("-" * 40)
    
    matching_data = {
        "synthetic_profile": synthetic_profile,
        "image_url": test_data_female['file_name']
    }
    
    try:
        matching_response = requests.post(
            "http://localhost:8200/find-profile-matches", 
            json=matching_data, 
            timeout=120
        )
        
        if matching_response.status_code != 200:
            print(f"❌ Profile matching failed: {matching_response.status_code}")
            return False
        
        matching_result = matching_response.json()
        matches = matching_result['data']['matches']
        
        print(f"✅ Found {len(matches)} matches")
        
        # Verify all matches have the same gender as synthetic profile (male)
        if matches:
            print(f"\n🔍 Verifying Match Genders:")
            all_correct = True
            for i, match in enumerate(matches[:3], 1):
                match_gender = match.get('gender', 'unknown')
                match_name = match.get('first_name', 'Unknown')
                print(f"   {i}. {match_name}: gender = '{match_gender}' (should be 'm')")
                
                if match_gender != 'm':
                    print(f"      ❌ ERROR: Expected 'm', got '{match_gender}'")
                    all_correct = False
                else:
                    print(f"      ✅ Correct gender")
            
            if all_correct:
                print(f"\n✅ All matches have correct gender (m)")
                print(f"✅ Gender matching logic is working correctly!")
                return True
            else:
                print(f"\n❌ Some matches have incorrect gender")
                return False
        else:
            print(f"⚠️  No matches found to verify gender")
            return True  # No matches is not necessarily an error
        
    except Exception as e:
        print(f"❌ Error in profile matching: {str(e)}")
        return False

def test_male_input():
    """Test with male input to verify it creates female synthetic profile"""
    
    print(f"\n" + "=" * 60)
    print("🔍 Testing Male Input → Female Synthetic Profile")
    print("=" * 60)
    
    # Test with male input - should create female synthetic profile
    test_data_male = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "John",
        "last_name": "Smith", 
        "height_cm": 180.0,
        "age": 25,
        "status": "single",
        "gender": "m",  # Original: male
        "orientation": "straight",
        "body_type": "athletic",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "engineer",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs",
        "desired_relationship": "long-term dating",
        "openness": 8.0,
        "conscientiousness": 7.5,
        "extraversion": 6.5,
        "agreeableness": 8.0,
        "neuroticism": 3.0,
        "interests": "Basketball, Gaming, Cooking, Travel",
        "essay0": "I enjoy staying active and exploring new technologies.",
        "personality_tags": "Analytical, Outgoing, Reliable, Creative"
    }
    
    print(f"📊 Original Profile:")
    print(f"   👤 Name: {test_data_male['first_name']} {test_data_male['last_name']}")
    print(f"   ⚧️  Original Gender: {test_data_male['gender']} (male)")
    print(f"   📏 Height: {test_data_male['height_cm']} cm")
    
    try:
        profile_response = requests.post(
            "http://localhost:8200/create-synthetic-profile", 
            json=test_data_male, 
            timeout=30
        )
        
        if profile_response.status_code != 200:
            print(f"❌ Profile creation failed: {profile_response.status_code}")
            return False
        
        profile_result = profile_response.json()
        synthetic_profile = profile_result['data']['synthetic_profile']
        
        print(f"\n✅ Synthetic profile created")
        print(f"   ⚧️  Synthetic Gender: {synthetic_profile['gender']} (should be 'f' - female)")
        print(f"   📏 Synthetic Height: {synthetic_profile['height_cm']} cm")
        
        # Verify gender was flipped correctly
        expected_gender = 'f'  # Male should flip to female
        if synthetic_profile['gender'] != expected_gender:
            print(f"❌ ERROR: Expected gender '{expected_gender}', got '{synthetic_profile['gender']}'")
            return False
        
        print(f"✅ Gender flip correct: m → f")
        return True
        
    except Exception as e:
        print(f"❌ Error in profile creation: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Gender Fix Test")
    
    # Test 1: Female input
    success1 = test_gender_fix()
    
    # Test 2: Male input  
    success2 = test_male_input()
    
    print(f"\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    
    if success1 and success2:
        print(f"🎉 All tests passed!")
        print(f"✅ Female → Male synthetic profile: WORKING")
        print(f"✅ Male → Female synthetic profile: WORKING")
        print(f"✅ Gender matching logic: FIXED")
        print(f"\n🔧 The double gender flip bug has been resolved!")
    else:
        print(f"❌ Some tests failed!")
        print(f"{'✅' if success1 else '❌'} Female → Male test: {'PASSED' if success1 else 'FAILED'}")
        print(f"{'✅' if success2 else '❌'} Male → Female test: {'PASSED' if success2 else 'FAILED'}")
