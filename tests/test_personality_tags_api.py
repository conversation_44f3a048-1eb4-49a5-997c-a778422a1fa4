#!/usr/bin/env python3
"""
Test script for the new generate-personality-tags API endpoint.
This tests the cloned personality tags generation logic.
"""

import requests
import json
import time

def test_personality_tags_generation():
    """Test the generate-personality-tags API endpoint"""
    
    print("🔍 Testing Generate Personality Tags API")
    print("=" * 60)
    
    # Test cases with different personality trait combinations
    test_cases = [
        {
            "name": "High Scores",
            "data": {
                "openness": 9.0,
                "conscientiousness": 8.5,
                "extraversion": 9.5,
                "agreeableness": 8.0
            },
            "expected_tags": ["Imaginative", "Organised", "Outgoing", "Warm"]
        },
        {
            "name": "Medium Scores", 
            "data": {
                "openness": 5.0,
                "conscientiousness": 6.0,
                "extraversion": 4.5,
                "agreeableness": 7.0
            },
            "expected_tags": ["Inquisitive", "Adaptable", "Friendly", "Considerate"]
        },
        {
            "name": "Low Scores",
            "data": {
                "openness": 2.0,
                "conscientiousness": 1.5,
                "extraversion": 3.0,
                "agreeableness": 2.5
            },
            "expected_tags": ["Conventional", "Spontaneous", "Reserved", "Independent"]
        },
        {
            "name": "Mixed Scores",
            "data": {
                "openness": 8.5,  # High -> Imaginative
                "conscientiousness": 5.0,  # Medium -> Adaptable
                "extraversion": 2.0,  # Low -> Reserved
                "agreeableness": 9.0   # High -> Warm
            },
            "expected_tags": ["Imaginative", "Adaptable", "Reserved", "Warm"]
        },
        {
            "name": "Boundary Values",
            "data": {
                "openness": 3.0,   # Low (boundary)
                "conscientiousness": 7.0,   # Medium (boundary)
                "extraversion": 7.1,  # High (just above boundary)
                "agreeableness": 3.1   # Medium (just above boundary)
            },
            "expected_tags": ["Conventional", "Adaptable", "Outgoing", "Considerate"]
        }
    ]
    
    api_url = "http://localhost:8200/generate-personality-tags"
    
    all_tests_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📤 Test {i}: {test_case['name']}")
        print(f"   Input: {test_case['data']}")
        
        try:
            start_time = time.time()
            response = requests.post(api_url, json=test_case['data'], timeout=10)
            end_time = time.time()
            
            print(f"   ⏱️  Response time: {end_time - start_time:.3f} seconds")
            print(f"   📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                
                personality_tags = data.get('personality_tags', '')
                personality_tags_list = data.get('personality_tags_list', [])
                trait_scores_used = data.get('trait_scores_used', {})
                score_categories = data.get('score_categories', {})
                generation_time = result.get('generation_time_seconds', 0)
                
                print(f"   ✅ Success!")
                print(f"   🏷️  Generated tags: {personality_tags}")
                print(f"   📊 Neuroticism added: {trait_scores_used.get('neuroticism', 'N/A')}")
                print(f"   ⚡ Generation time: {generation_time:.6f} seconds")
                
                # Verify the first 4 tags match expected (neuroticism is random)
                expected_first_four = test_case['expected_tags']
                actual_first_four = personality_tags_list[:4]
                
                if actual_first_four == expected_first_four:
                    print(f"   ✅ Tags match expected: {expected_first_four}")
                else:
                    print(f"   ❌ Tags don't match!")
                    print(f"      Expected: {expected_first_four}")
                    print(f"      Actual: {actual_first_four}")
                    all_tests_passed = False
                
                # Verify score categories
                print(f"   📈 Score categories:")
                for trait, category in score_categories.items():
                    if trait != 'neuroticism':  # Skip neuroticism as it's random
                        print(f"      {trait}: {category}")
                
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response: {response.text}")
                all_tests_passed = False
                
        except requests.exceptions.Timeout:
            print("   ⏰ Request timed out")
            all_tests_passed = False
        except requests.exceptions.ConnectionError:
            print("   🔌 Connection error - make sure API is running")
            all_tests_passed = False
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
            all_tests_passed = False
    
    return all_tests_passed

def test_input_validation():
    """Test input validation for the personality tags API"""
    
    print(f"\n" + "=" * 60)
    print("🔍 Testing Input Validation")
    print("=" * 60)
    
    # Test cases with invalid inputs
    invalid_test_cases = [
        {
            "name": "Negative openness",
            "data": {
                "openness": -1.0,
                "conscientiousness": 5.0,
                "extraversion": 5.0,
                "agreeableness": 5.0
            },
            "expected_error": "Invalid openness score"
        },
        {
            "name": "Too high conscientiousness",
            "data": {
                "openness": 5.0,
                "conscientiousness": 11.0,
                "extraversion": 5.0,
                "agreeableness": 5.0
            },
            "expected_error": "Invalid conscientiousness score"
        },
        {
            "name": "Missing field",
            "data": {
                "openness": 5.0,
                "conscientiousness": 5.0,
                "extraversion": 5.0
                # Missing agreeableness
            },
            "expected_error": "validation error"
        }
    ]
    
    api_url = "http://localhost:8200/generate-personality-tags"
    validation_passed = True
    
    for i, test_case in enumerate(invalid_test_cases, 1):
        print(f"\n📤 Validation Test {i}: {test_case['name']}")
        print(f"   Input: {test_case['data']}")
        
        try:
            response = requests.post(api_url, json=test_case['data'], timeout=10)
            
            print(f"   📊 Status code: {response.status_code}")
            
            if response.status_code == 400 or response.status_code == 422:
                print(f"   ✅ Correctly rejected invalid input")
                print(f"   📝 Error message: {response.text[:100]}...")
            else:
                print(f"   ❌ Should have rejected invalid input but got: {response.status_code}")
                validation_passed = False
                
        except Exception as e:
            print(f"   💥 Error: {str(e)}")
            validation_passed = False
    
    return validation_passed

def test_consistency_with_synthetic_profile():
    """Test that the API produces the same results as the synthetic profile generation"""
    
    print(f"\n" + "=" * 60)
    print("🔍 Testing Consistency with Synthetic Profile Generation")
    print("=" * 60)
    
    # Test data for creating a synthetic profile
    profile_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "TestUser",
        "last_name": "ForConsistency", 
        "height_cm": 165.0,
        "age": 25,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "average",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "tester",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs",
        "desired_relationship": "long-term dating",
        "openness": 6.5,
        "conscientiousness": 7.2,
        "extraversion": 4.8,
        "agreeableness": 8.1,
        "neuroticism": 3.5,
        "interests": "Testing, Validation, Quality Assurance",
        "essay0": "I love ensuring things work correctly.",
        "personality_tags": "Original, Tags, For, Testing"
    }
    
    try:
        # Create synthetic profile
        print(f"📤 Creating synthetic profile...")
        profile_response = requests.post(
            "http://localhost:8200/create-synthetic-profile", 
            json=profile_data, 
            timeout=30
        )
        
        if profile_response.status_code != 200:
            print(f"❌ Synthetic profile creation failed: {profile_response.status_code}")
            return False
        
        synthetic_result = profile_response.json()
        synthetic_profile = synthetic_result['data']['synthetic_profile']
        
        # Extract the personality scores from synthetic profile
        personality_scores = {
            "openness": synthetic_profile['openness'],
            "conscientiousness": synthetic_profile['conscientiousness'],
            "extraversion": synthetic_profile['extraversion'],
            "agreeableness": synthetic_profile['agreeableness']
        }
        
        print(f"✅ Synthetic profile created")
        print(f"   Personality scores: {personality_scores}")
        print(f"   Generated tags: {synthetic_profile['personality_tags']}")
        
        # Now test the standalone API with the same scores
        print(f"\n📤 Testing standalone API with same scores...")
        tags_response = requests.post(
            "http://localhost:8200/generate-personality-tags",
            json=personality_scores,
            timeout=10
        )
        
        if tags_response.status_code != 200:
            print(f"❌ Personality tags API failed: {tags_response.status_code}")
            return False
        
        tags_result = tags_response.json()
        standalone_tags = tags_result['data']['personality_tags']
        
        print(f"✅ Standalone API completed")
        print(f"   Generated tags: {standalone_tags}")
        
        # Compare the first 4 tags (neuroticism is random so might differ)
        synthetic_tags_list = synthetic_profile['personality_tags'].split(', ')
        standalone_tags_list = standalone_tags.split(', ')
        
        first_four_synthetic = synthetic_tags_list[:4]
        first_four_standalone = standalone_tags_list[:4]
        
        if first_four_synthetic == first_four_standalone:
            print(f"\n✅ CONSISTENCY TEST PASSED!")
            print(f"   Both methods produce identical first 4 tags: {first_four_synthetic}")
            return True
        else:
            print(f"\n❌ CONSISTENCY TEST FAILED!")
            print(f"   Synthetic profile tags: {first_four_synthetic}")
            print(f"   Standalone API tags: {first_four_standalone}")
            return False
        
    except Exception as e:
        print(f"❌ Error during consistency test: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Personality Tags API Tests")
    
    # Test 1: Basic functionality
    success1 = test_personality_tags_generation()
    
    # Test 2: Input validation
    success2 = test_input_validation()
    
    # Test 3: Consistency with synthetic profile
    success3 = test_consistency_with_synthetic_profile()
    
    print(f"\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    
    if success1 and success2 and success3:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"✅ Personality tags generation: WORKING")
        print(f"✅ Input validation: WORKING")
        print(f"✅ Consistency with synthetic profile: VERIFIED")
        print(f"\n🔧 API Usage:")
        print(f"   POST /generate-personality-tags")
        print(f"   Input: openness, conscientiousness, extraversion, agreeableness (0-10)")
        print(f"   Output: personality_tags string + detailed breakdown")
    else:
        print(f"❌ Some tests failed!")
        print(f"{'✅' if success1 else '❌'} Basic functionality: {'PASSED' if success1 else 'FAILED'}")
        print(f"{'✅' if success2 else '❌'} Input validation: {'PASSED' if success2 else 'FAILED'}")
        print(f"{'✅' if success3 else '❌'} Consistency test: {'PASSED' if success3 else 'FAILED'}")
