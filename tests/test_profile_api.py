#!/usr/bin/env python3
"""
Test script for the new profile retrieval API endpoints.
Tests both single profile retrieval by ID and all profiles with pagination.
"""

import requests
import json
import time

def test_get_profile_by_id():
    """Test the get profile by ID endpoint"""
    
    print("🔍 Testing Get Profile by ID API")
    print("=" * 50)
    
    # Test cases with different profile IDs
    test_ids = [17, 112, 20, 1, 999]  # Last one should not exist
    
    for profile_id in test_ids:
        print(f"\n📤 Testing profile ID: {profile_id}")
        
        try:
            start_time = time.time()
            response = requests.get(f"http://localhost:8200/profile/{profile_id}", timeout=10)
            end_time = time.time()
            
            print(f"⏱️  Response time: {end_time - start_time:.3f} seconds")
            print(f"📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                profile_data = result.get('data', {})
                
                print(f"✅ Success!")
                print(f"   👤 Name: {profile_data.get('first_name')} {profile_data.get('last_name')}")
                print(f"   ⚧️  Gender: {profile_data.get('gender')}")
                print(f"   🎂 Age: {profile_data.get('age')}")
                print(f"   📏 Height: {profile_data.get('height_cm')} cm")
                print(f"   📍 Location: {profile_data.get('location')}")
                print(f"   🎯 Interests: {profile_data.get('interests', '')[:50]}{'...' if len(profile_data.get('interests', '')) > 50 else ''}")
                
            elif response.status_code == 404:
                print(f"❌ Profile not found (expected for ID {profile_id})")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out")
        except requests.exceptions.ConnectionError:
            print("🔌 Connection error - make sure API is running")
        except Exception as e:
            print(f"💥 Error: {str(e)}")

def test_get_all_profiles():
    """Test the get all profiles endpoint with pagination"""
    
    print(f"\n" + "=" * 60)
    print("🔍 Testing Get All Profiles API")
    print("=" * 60)
    
    # Test cases with different pagination parameters
    test_cases = [
        {"skip": 0, "limit": 5, "description": "First 5 profiles"},
        {"skip": 5, "limit": 3, "description": "Next 3 profiles (skip 5)"},
        {"skip": 0, "limit": 100, "description": "All profiles (default limit)"},
        {"skip": 95, "limit": 10, "description": "Last few profiles"}
    ]
    
    for test_case in test_cases:
        skip = test_case["skip"]
        limit = test_case["limit"]
        description = test_case["description"]
        
        print(f"\n📤 Testing: {description}")
        print(f"   Parameters: skip={skip}, limit={limit}")
        
        try:
            start_time = time.time()
            response = requests.get(
                f"http://localhost:8200/profiles?skip={skip}&limit={limit}", 
                timeout=10
            )
            end_time = time.time()
            
            print(f"⏱️  Response time: {end_time - start_time:.3f} seconds")
            print(f"📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                profiles = data.get('profiles', [])
                count = data.get('count', 0)
                
                print(f"✅ Success!")
                print(f"   📊 Profiles returned: {count}")
                print(f"   📄 Skip: {data.get('skip', 0)}")
                print(f"   📄 Limit: {data.get('limit', 0)}")
                
                if profiles:
                    print(f"   👥 Sample profiles:")
                    for i, profile in enumerate(profiles[:3], 1):
                        name = f"{profile.get('first_name')} {profile.get('last_name')}"
                        gender = profile.get('gender')
                        age = profile.get('age')
                        print(f"      {i}. {name} ({gender}, {age})")
                
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out")
        except requests.exceptions.ConnectionError:
            print("🔌 Connection error - make sure API is running")
        except Exception as e:
            print(f"💥 Error: {str(e)}")

def test_profile_data_integrity():
    """Test that profile data is correctly parsed and formatted"""
    
    print(f"\n" + "=" * 60)
    print("🔍 Testing Profile Data Integrity")
    print("=" * 60)
    
    # Get a specific profile to check data types and completeness
    profile_id = 17  # James Hall from the CSV
    
    try:
        response = requests.get(f"http://localhost:8200/profile/{profile_id}", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            profile_data = result.get('data', {})
            
            print(f"✅ Testing profile ID {profile_id}")
            print(f"   👤 Name: {profile_data.get('first_name')} {profile_data.get('last_name')}")
            
            # Check data types
            checks = [
                ("ID", profile_data.get('id'), int),
                ("Height", profile_data.get('height_cm'), float),
                ("Age", profile_data.get('age'), int),
                ("Openness", profile_data.get('openness'), float),
                ("Conscientiousness", profile_data.get('conscientiousness'), float),
                ("Extraversion", profile_data.get('extraversion'), float),
                ("Agreeableness", profile_data.get('agreeableness'), float),
                ("Neuroticism", profile_data.get('neuroticism'), float),
            ]
            
            print(f"\n🔍 Data Type Validation:")
            all_correct = True
            for field_name, value, expected_type in checks:
                if isinstance(value, expected_type):
                    print(f"   ✅ {field_name}: {value} ({type(value).__name__})")
                else:
                    print(f"   ❌ {field_name}: {value} (expected {expected_type.__name__}, got {type(value).__name__})")
                    all_correct = False
            
            # Check required fields are present
            required_fields = [
                'id', 'file_name', 'first_name', 'last_name', 'height_cm', 'age',
                'status', 'gender', 'orientation', 'body_type', 'diet', 'drinks',
                'smokes', 'education', 'job', 'location', 'family_plan', 'pets',
                'desired_relationship', 'openness', 'conscientiousness', 'extraversion',
                'agreeableness', 'neuroticism', 'interests', 'essay0', 'personality_tags'
            ]
            
            print(f"\n🔍 Required Fields Check:")
            missing_fields = []
            for field in required_fields:
                if field in profile_data:
                    print(f"   ✅ {field}: Present")
                else:
                    print(f"   ❌ {field}: Missing")
                    missing_fields.append(field)
                    all_correct = False
            
            if all_correct:
                print(f"\n🎉 All data integrity checks passed!")
            else:
                print(f"\n❌ Some data integrity issues found")
                if missing_fields:
                    print(f"   Missing fields: {missing_fields}")
            
            return all_correct
            
        else:
            print(f"❌ Failed to get profile for integrity test: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 Error during integrity test: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Profile API Tests")
    
    # Test individual profile retrieval
    test_get_profile_by_id()
    
    # Test all profiles with pagination
    test_get_all_profiles()
    
    # Test data integrity
    integrity_passed = test_profile_data_integrity()
    
    print(f"\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    
    print(f"✅ Profile by ID API: Available")
    print(f"✅ All profiles API: Available")
    print(f"{'✅' if integrity_passed else '❌'} Data integrity: {'PASSED' if integrity_passed else 'FAILED'}")
    
    print(f"\n🔧 API Usage:")
    print(f"   GET /profile/{{id}} - Get single profile by ID")
    print(f"   GET /profiles?skip=0&limit=10 - Get all profiles with pagination")
    
    if integrity_passed:
        print(f"\n🎉 All profile API tests completed successfully!")
    else:
        print(f"\n⚠️  Some tests had issues - check the output above")
