#!/usr/bin/env python3
"""
Quick test to verify the face embedding API is working with the correct embedding file.
"""

import requests
import json
import time

def test_quick_embedding():
    """Quick test of the face embedding API"""
    
    print("🔍 Testing Face Embedding API with Correct Embedding File")
    print("=" * 60)
    
    # API endpoint
    api_url = "http://localhost:8200/create-synthetic-profile-matching"
    
    # Simple test data
    test_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "Test",
        "last_name": "User", 
        "height_cm": 165.0,
        "age": 25,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "average",
        "diet": "anything",
        "drinks": "socially",
        "smokes": "no",
        "education": "college/university",
        "job": "student",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs and cats",
        "desired_relationship": "long-term dating",
        "openness": 7.0,
        "conscientiousness": 6.0,
        "extraversion": 7.0,
        "agreeableness": 7.0,
        "neuroticism": 4.0,
        "interests": "Reading, Yoga",
        "essay0": "I love exploring new places and trying different cuisines.",
        "personality_tags": "Adventurous, Creative, Friendly"
    }
    
    try:
        print(f"📤 Sending request to: {api_url}")
        print(f"📸 Test image: {test_data['file_name']}")
        
        start_time = time.time()
        response = requests.post(api_url, json=test_data, timeout=60)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")

            # Print the structure to debug
            print(f"📋 Response keys: {list(result.keys())}")

            # Check if we have matches
            data = result.get('data', {})
            matches = data.get('matches', [])
            print(f"🎯 Found {len(matches)} matches")

            try:
                if matches:
                    top_match = matches[0]
                    print(f"🏆 Top match: {top_match.get('first_name', 'Unknown')} (Score: {top_match.get('total_score', 0):.3f})")

                    # Check if face embedding was processed
                    synthetic_profile = data.get('synthetic_profile', {})
                    print(f"👤 Synthetic profile gender: {synthetic_profile.get('gender', 'Unknown')}")

                    # Check execution time
                    execution_time = result.get('execution_time_seconds', 0)
                    print(f"⚡ Total execution time: {execution_time:.2f} seconds")

                    print(f"\n✅ API is working correctly with the embedding file!")
                    return True
                else:
                    print(f"⚠️  No matches found, but API responded successfully")
                    print(f"📋 Data keys: {list(data.keys())}")
                    return True
            except Exception as e:
                print(f"💥 Error processing response: {str(e)}")
                print(f"📋 Raw response: {result}")
                return False
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - make sure API is running")
        return False
    except Exception as e:
        print(f"💥 Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_quick_embedding()
    if success:
        print(f"\n🎉 Test completed successfully!")
    else:
        print(f"\n❌ Test failed!")
