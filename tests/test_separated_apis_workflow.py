#!/usr/bin/env python3
"""
Test script demonstrating the complete workflow using the two separated APIs:
1. Create synthetic profile
2. Find matches for the synthetic profile
"""

import requests
import json
import time

def test_complete_workflow():
    """Test the complete workflow using both separated APIs"""
    
    print("🔍 Testing Complete Separated APIs Workflow")
    print("=" * 60)
    
    # Test data for profile creation
    profile_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>", 
        "height_cm": 162.0,
        "age": 24,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "slim",
        "diet": "vegetarian",
        "drinks": "socially",
        "smokes": "no",
        "education": "masters",
        "job": "software engineer",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs",
        "desired_relationship": "long-term dating",
        "openness": 8.5,
        "conscientiousness": 7.0,
        "extraversion": 6.5,
        "agreeableness": 8.0,
        "neuroticism": 3.5,
        "interests": "Rock climbing, Photography, Cooking, Board games, Travel",
        "essay0": "I'm passionate about technology and love solving complex problems. When I'm not coding, you'll find me exploring new hiking trails or trying out new recipes.",
        "personality_tags": "Analytical, Creative, Adventurous, Thoughtful, Independent"
    }
    
    print(f"👤 Original Profile: {profile_data['first_name']} {profile_data['last_name']}")
    print(f"   Gender: {profile_data['gender']}, Height: {profile_data['height_cm']} cm")
    print(f"   Interests: {profile_data['interests']}")
    
    # Step 1: Create synthetic profile
    print(f"\n🔄 Step 1: Creating Synthetic Profile")
    print("-" * 40)
    
    try:
        start_time = time.time()
        profile_response = requests.post(
            "http://localhost:8200/create-synthetic-profile", 
            json=profile_data, 
            timeout=30
        )
        profile_creation_time = time.time() - start_time
        
        if profile_response.status_code != 200:
            print(f"❌ Profile creation failed: {profile_response.status_code}")
            print(f"Response: {profile_response.text}")
            return False
        
        profile_result = profile_response.json()
        synthetic_profile = profile_result['data']['synthetic_profile']
        modifications = profile_result['data']['modifications']
        
        print(f"✅ Synthetic profile created in {profile_creation_time:.2f} seconds")
        print(f"   🔄 {modifications['gender_flipped']}")
        print(f"   📏 {modifications['height_adjusted']}")
        print(f"   🎯 New interests: {modifications['synthetic_interests']}")
        print(f"   🏷️  New personality: {modifications['synthetic_personality_tags']}")
        
    except Exception as e:
        print(f"❌ Error in profile creation: {str(e)}")
        return False
    
    # Step 2: Find matches
    print(f"\n🎯 Step 2: Finding Profile Matches")
    print("-" * 40)
    
    matching_data = {
        "synthetic_profile": synthetic_profile,
        "image_url": profile_data['file_name']
    }
    
    try:
        start_time = time.time()
        matching_response = requests.post(
            "http://localhost:8200/find-profile-matches", 
            json=matching_data, 
            timeout=120
        )
        matching_time = time.time() - start_time
        
        if matching_response.status_code != 200:
            print(f"❌ Profile matching failed: {matching_response.status_code}")
            print(f"Response: {matching_response.text}")
            return False
        
        matching_result = matching_response.json()
        matches = matching_result['data']['matches']
        total_candidates = matching_result['data']['total_candidates']
        
        print(f"✅ Found {total_candidates} matches in {matching_time:.2f} seconds")
        
        # Display top matches
        if matches:
            print(f"\n🏆 Top 5 Matches:")
            for i, match in enumerate(matches[:5], 1):
                name = match.get('first_name', 'Unknown')
                score = match.get('total_score', 0)
                explanation = match.get('explanation', 'No explanation')
                
                print(f"\n   {i}. {name} (Score: {score:.3f})")
                print(f"      💬 {explanation}")
        
        # Calculate total time
        total_time = profile_creation_time + matching_time
        
        print(f"\n📊 Performance Summary:")
        print(f"   ⚡ Profile creation: {profile_creation_time:.2f} seconds")
        print(f"   ⚡ Profile matching: {matching_time:.2f} seconds")
        print(f"   ⚡ Total workflow time: {total_time:.2f} seconds")
        
        # Save complete workflow result
        workflow_result = {
            "original_profile": profile_data,
            "synthetic_profile_result": profile_result,
            "matching_result": matching_result,
            "performance": {
                "profile_creation_time": profile_creation_time,
                "matching_time": matching_time,
                "total_time": total_time
            }
        }
        
        with open('tests/complete_workflow_result.json', 'w') as f:
            json.dump(workflow_result, f, indent=2)
        
        print(f"\n💾 Complete workflow results saved to 'tests/complete_workflow_result.json'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in profile matching: {str(e)}")
        return False

def compare_with_legacy_api():
    """Compare the separated APIs with the legacy combined API"""
    
    print(f"\n🔄 Comparing with Legacy Combined API")
    print("=" * 50)
    
    # Same test data
    legacy_data = {
        "file_name": "https://storage.googleapis.com/comfyui-data-analytic-project-424703/comfyui/common/F_21_image_85.png",
        "first_name": "Sarah",
        "last_name": "Chen", 
        "height_cm": 162.0,
        "age": 24,
        "status": "single",
        "gender": "f",
        "orientation": "straight",
        "body_type": "slim",
        "diet": "vegetarian",
        "drinks": "socially",
        "smokes": "no",
        "education": "masters",
        "job": "software engineer",
        "location": "san francisco, california",
        "family_plan": "wants kids",
        "pets": "likes dogs",
        "desired_relationship": "long-term dating",
        "openness": 8.5,
        "conscientiousness": 7.0,
        "extraversion": 6.5,
        "agreeableness": 8.0,
        "neuroticism": 3.5,
        "interests": "Rock climbing, Photography, Cooking, Board games, Travel",
        "essay0": "I'm passionate about technology and love solving complex problems.",
        "personality_tags": "Analytical, Creative, Adventurous, Thoughtful, Independent"
    }
    
    try:
        start_time = time.time()
        legacy_response = requests.post(
            "http://localhost:8200/create-synthetic-profile-matching", 
            json=legacy_data, 
            timeout=120
        )
        legacy_time = time.time() - start_time
        
        if legacy_response.status_code == 200:
            print(f"✅ Legacy API completed in {legacy_time:.2f} seconds")
            print(f"📊 Both approaches work - choose based on your needs:")
            print(f"   🔗 Separated APIs: Better for modular applications")
            print(f"   🏢 Legacy API: Better for simple one-call workflows")
        else:
            print(f"⚠️  Legacy API test failed: {legacy_response.status_code}")
            
    except Exception as e:
        print(f"⚠️  Legacy API test error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Complete Workflow Test")
    
    success = test_complete_workflow()
    
    if success:
        print(f"\n🎉 Complete workflow test successful!")
        compare_with_legacy_api()
        
        print(f"\n📋 Summary:")
        print(f"   ✅ Separated APIs working correctly")
        print(f"   ✅ Profile creation: PASSED")
        print(f"   ✅ Profile matching: PASSED")
        print(f"   ✅ Complete workflow: PASSED")
        print(f"   ✅ Results saved for review")
        
        print(f"\n🔧 API Usage:")
        print(f"   1. POST /create-synthetic-profile - Create synthetic profiles")
        print(f"   2. POST /find-profile-matches - Find matches for profiles")
        print(f"   3. POST /create-synthetic-profile-matching - Legacy combined API")
        
    else:
        print(f"\n❌ Workflow test failed!")
