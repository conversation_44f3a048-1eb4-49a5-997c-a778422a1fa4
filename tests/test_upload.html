<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background-color: #fafafa;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .image-preview {
            max-width: 300px;
            max-height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .url-link {
            word-break: break-all;
            color: #007bff;
            text-decoration: none;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image Upload to Google Cloud Storage</h1>
        
        <div class="upload-area">
            <h3>Select an image to upload</h3>
            <p>Supported formats: JPEG, PNG, GIF, WebP, BMP, TIFF</p>
            <p>Maximum file size: 10MB</p>
            <input type="file" id="fileInput" accept="image/*" />
            <br><br>
            <button onclick="uploadImage()" id="uploadBtn">Upload Image</button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Uploading image...</p>
        </div>

        <div id="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8200';

        function uploadImage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            const uploadBtn = document.getElementById('uploadBtn');

            if (!file) {
                showResult('Please select a file to upload.', 'error');
                return;
            }

            // Show loading state
            loadingDiv.style.display = 'block';
            uploadBtn.disabled = true;
            resultDiv.innerHTML = '';

            const formData = new FormData();
            formData.append('file', file);

            fetch(`${API_BASE_URL}/upload-image`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingDiv.style.display = 'none';
                uploadBtn.disabled = false;

                if (data.success) {
                    showResult(`
                        <h3>Upload Successful!</h3>
                        <p><strong>Original filename:</strong> ${data.data.original_filename}</p>
                        <p><strong>Uploaded filename:</strong> ${data.data.uploaded_filename}</p>
                        <p><strong>File size:</strong> ${(data.data.file_size_bytes / 1024).toFixed(2)} KB</p>
                        <p><strong>Content type:</strong> ${data.data.content_type}</p>
                        <p><strong>Public URL:</strong> <a href="${data.data.public_url}" target="_blank" class="url-link">${data.data.public_url}</a></p>
                        <img src="${data.data.public_url}" alt="Uploaded image" class="image-preview" />
                    `, 'success');
                } else {
                    showResult(`Upload failed: ${data.detail || 'Unknown error'}`, 'error');
                }
            })
            .catch(error => {
                loadingDiv.style.display = 'none';
                uploadBtn.disabled = false;
                console.error('Error:', error);
                showResult(`Upload failed: ${error.message}`, 'error');
            });
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // Allow drag and drop
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('fileInput');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f0f8ff';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '#fafafa';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '#fafafa';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
            }
        });

        // Check API health on page load
        fetch(`${API_BASE_URL}/health`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'healthy') {
                    console.log('API is healthy and ready');
                } else {
                    showResult('API is not healthy. Please check the server.', 'error');
                }
            })
            .catch(error => {
                showResult('Cannot connect to API. Please make sure the server is running on http://localhost:8200', 'error');
            });
    </script>
</body>
</html>
